{"name": "@openai/agents-realtime", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.0.9", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows. This package contains the logic for building realtime voice agents on the server or in the browser.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "browser": "./dist/bundle/openai-realtime-agents.umd.cjs", "exports": {".": {"browser": {"require": "./dist/index.js", "types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "./_shims": {"browser": {"require": "./dist/shims/shims-browser.js", "types": "./dist/shims/shims-browser.d.ts", "default": "./dist/shims/shims-browser.mjs"}, "node": {"require": "./dist/shims/shims-node.js", "types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.mjs"}, "require": {"types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.js"}, "types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.mjs"}}, "typesVersions": {"*": {"_shims": ["dist/shims/shims-node.d.ts"]}}, "dependencies": {"@openai/zod": "npm:zod@^3.25.40", "@types/ws": "^8.18.1", "debug": "^4.4.0", "ws": "^8.18.1", "@openai/agents-core": "0.0.9"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "devDependencies": {"@types/debug": "^4.1.12", "vite": "^6.3.5"}, "files": ["dist"], "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json", "bundle": "vite build"}}