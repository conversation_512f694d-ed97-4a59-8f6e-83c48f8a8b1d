{"version": 3, "file": "openaiRealtimeWebRtc.mjs", "sourceRoot": "", "sources": ["../src/openaiRealtimeWebRtc.ts"], "names": [], "mappings": "AAAA,2BAA2B;OAEpB,EAAE,oBAAoB,EAAE,MAAM,4BAA4B;OAM1D,EAAE,SAAS,EAAE,MAAM,qBAAqB;OACxC,MAAM;OAEN,EACL,kBAAkB,GAEnB;OACM,EAAE,kBAAkB,EAAE;OACtB,EAAE,OAAO,EAAE;AAyDlB;;;;;;;GAOG;AACH,MAAM,OAAO,oBACX,SAAQ,kBAAkB;IAaG;IAV7B,IAAI,CAAS;IACb,MAAM,GAAgB;QACpB,MAAM,EAAE,cAAc;QACtB,cAAc,EAAE,SAAS;QACzB,WAAW,EAAE,SAAS;KACvB,CAAC;IACF,kBAAkB,CAAU;IAC5B,gBAAgB,GAAY,KAAK,CAAC;IAClC,MAAM,GAAG,KAAK,CAAC;IAEf,YAA6B,UAAuC,EAAE;QACpE,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE,CAAC;YAC7C,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QACD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJY,YAAO,GAAP,OAAO,CAAkC;QAKpE,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,IAAI,oCAAoC,CAAC;QACpE,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,OAAO,CAAC,OAA6C;QACzD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACvC,OAAO;QACT,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CACT,wEAAwE,CACzE,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAE9C,MAAM,WAAW,GAAG,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3E,IAAI,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,CAAC,WAAW,EAAE,CAAC;YACvE,MAAM,IAAI,SAAS,CACjB,2KAA2K,CAC5K,CAAC;QACJ,CAAC;QAED,qDAAqD;QACrD,OAAO,IAAI,OAAO,CAAO,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE;YACjD,IAAI,CAAC;gBACH,MAAM,iBAAiB,GAAmC;oBACxD,GAAG,CAAC,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;oBACvC,KAAK,EAAE,IAAI,CAAC,YAAY;iBACzB,CAAC;gBAEF,MAAM,aAAa,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEvC,IAAI,cAAc,GAAsB,IAAI,iBAAiB,EAAE,CAAC;gBAChE,MAAM,WAAW,GAAG,cAAc,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEnE,IAAI,CAAC,MAAM,GAAG;oBACZ,MAAM,EAAE,YAAY;oBACpB,cAAc;oBACd,WAAW;iBACZ,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBAEnD,WAAW,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;oBACxC,IAAI,CAAC,MAAM,GAAG;wBACZ,MAAM,EAAE,WAAW;wBACnB,cAAc;wBACd,WAAW;qBACZ,CAAC;oBACF,gFAAgF;oBAChF,sFAAsF;oBACtF,oFAAoF;oBACpF,uEAAuE;oBACvE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAC;oBAC5C,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;oBACnD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACf,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACrB,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,WAAW,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE;oBAChD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;oBACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;oBAC9D,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;wBACzB,OAAO;oBACT,CAAC;oBAED,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;wBACvC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;oBAC/B,CAAC;yBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;wBAC3C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;oBAChC,CAAC;oBAED,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;wBACtC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;wBAC7C,yDAAyD;wBACzD,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC;oBACjE,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,wBAAwB;gBACxB,MAAM,YAAY,GAChB,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBAC/D,YAAY,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC7B,cAAc,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;oBACjC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC;gBAEF,wBAAwB;gBACxB,MAAM,MAAM,GACV,IAAI,CAAC,OAAO,CAAC,WAAW;oBACxB,CAAC,MAAM,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;wBACzC,KAAK,EAAE,IAAI;qBACZ,CAAC,CAAC,CAAC;gBACN,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAEpD,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC;oBACtC,cAAc;wBACZ,MAAM,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;oBAC1D,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,cAAc,EAAE,CAAC;gBACnD,CAAC;gBAED,MAAM,KAAK,GAAG,MAAM,cAAc,CAAC,WAAW,EAAE,CAAC;gBACjD,MAAM,cAAc,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBAEhD,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBAC5C,CAAC;gBAED,MAAM,aAAa,GAAG;oBACpB,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC;oBAClD,KAAK,EAAE,IAAI,CAAC,YAAY;iBACzB,CAAC;gBAEF,MAAM,IAAI,GAAG,IAAI,QAAQ,EAAE,CAAC;gBAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC9B,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;gBAEtD,MAAM,WAAW,GAAG,MAAM,KAAK,CAAC,aAAa,EAAE;oBAC7C,MAAM,EAAE,MAAM;oBACd,IAAI,EAAE,IAAI;oBACV,OAAO,EAAE;wBACP,aAAa,EAAE,UAAU,MAAM,EAAE;wBACjC,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,CAAC;qBACtD;iBACF,CAAC,CAAC;gBAEH,MAAM,MAAM,GAA8B;oBACxC,IAAI,EAAE,QAAQ;oBACd,GAAG,EAAE,MAAM,WAAW,CAAC,IAAI,EAAE;iBAC9B,CAAC;gBAEF,MAAM,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;YACpD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBACrB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,KAA4B;QACpC,IACE,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW;YACxB,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,KAAK,MAAM,EAC7C,CAAC;YACD,MAAM,IAAI,KAAK,CACb,6FAA6F,CAC9F,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,KAAc;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YAClD,cAAc,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7C,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,KAAK,CAAC;gBAChC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QAClC,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;YAClD,cAAc,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC;YACvB,CAAC,CAAC,CAAC;YACH,cAAc,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,GAAG;gBACZ,MAAM,EAAE,cAAc;gBACtB,cAAc,EAAE,SAAS;gBACzB,WAAW,EAAE,SAAS;aACvB,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,SAAS;QACP,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,iBAAiB;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,2BAA2B;SAClC,CAAC,CAAC;IACL,CAAC;CACF"}