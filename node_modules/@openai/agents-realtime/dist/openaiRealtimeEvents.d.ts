import { z } from '@openai/zod/v3';
import type { MessageEvent as WebSocketMessageEvent } from 'ws';
export declare const realtimeResponse: z.ZodObject<{
    id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    conversation_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    max_output_tokens: z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodLiteral<"inf">]>>>;
    metadata: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
    modalities: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
    object: z.ZodNullable<z.ZodOptional<z.ZodLiteral<"realtime.response">>>;
    output: z.ZodNullable<z.ZodOptional<z.ZodArray<z.Z<PERSON>ny, "many">>>;
    output_audio_format: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    status: z.ZodNullable<z.ZodOptional<z.ZodEnum<["completed", "incomplete", "failed", "cancelled", "in_progress"]>>>;
    status_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
    usage: z.ZodNullable<z.ZodOptional<z.ZodObject<{
        input_tokens: z.ZodOptional<z.ZodNumber>;
        input_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        output_tokens: z.ZodOptional<z.ZodNumber>;
        output_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
    }, "strip", z.ZodTypeAny, {
        input_tokens?: number | undefined;
        input_tokens_details?: Record<string, any> | null | undefined;
        output_tokens?: number | undefined;
        output_tokens_details?: Record<string, any> | null | undefined;
    }, {
        input_tokens?: number | undefined;
        input_tokens_details?: Record<string, any> | null | undefined;
        output_tokens?: number | undefined;
        output_tokens_details?: Record<string, any> | null | undefined;
    }>>>;
    voice: z.ZodNullable<z.ZodOptional<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    object?: "realtime.response" | null | undefined;
    status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
    output?: any[] | null | undefined;
    id?: string | null | undefined;
    conversation_id?: string | null | undefined;
    max_output_tokens?: number | "inf" | null | undefined;
    metadata?: Record<string, any> | null | undefined;
    modalities?: string[] | null | undefined;
    output_audio_format?: string | null | undefined;
    status_details?: Record<string, any> | null | undefined;
    usage?: {
        input_tokens?: number | undefined;
        input_tokens_details?: Record<string, any> | null | undefined;
        output_tokens?: number | undefined;
        output_tokens_details?: Record<string, any> | null | undefined;
    } | null | undefined;
    voice?: string | null | undefined;
}, {
    object?: "realtime.response" | null | undefined;
    status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
    output?: any[] | null | undefined;
    id?: string | null | undefined;
    conversation_id?: string | null | undefined;
    max_output_tokens?: number | "inf" | null | undefined;
    metadata?: Record<string, any> | null | undefined;
    modalities?: string[] | null | undefined;
    output_audio_format?: string | null | undefined;
    status_details?: Record<string, any> | null | undefined;
    usage?: {
        input_tokens?: number | undefined;
        input_tokens_details?: Record<string, any> | null | undefined;
        output_tokens?: number | undefined;
        output_tokens_details?: Record<string, any> | null | undefined;
    } | null | undefined;
    voice?: string | null | undefined;
}>;
export declare const conversationItemContentSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
    type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
}, "strip", z.ZodTypeAny, {
    type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
    text?: string | null | undefined;
    audio?: string | null | undefined;
    transcript?: string | null | undefined;
    id?: string | undefined;
}, {
    type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
    text?: string | null | undefined;
    audio?: string | null | undefined;
    transcript?: string | null | undefined;
    id?: string | undefined;
}>;
export declare const conversationItemSchema: z.ZodObject<{
    id: z.ZodOptional<z.ZodString>;
    arguments: z.ZodOptional<z.ZodString>;
    call_id: z.ZodOptional<z.ZodString>;
    content: z.ZodOptional<z.ZodArray<z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
        type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
    }, "strip", z.ZodTypeAny, {
        type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
        text?: string | null | undefined;
        audio?: string | null | undefined;
        transcript?: string | null | undefined;
        id?: string | undefined;
    }, {
        type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
        text?: string | null | undefined;
        audio?: string | null | undefined;
        transcript?: string | null | undefined;
        id?: string | undefined;
    }>, "many">>;
    name: z.ZodOptional<z.ZodString>;
    object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
    output: z.ZodOptional<z.ZodString>;
    role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
    status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
    type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
}, "strip", z.ZodTypeAny, {
    object?: "realtime.item" | undefined;
    type?: "message" | "function_call" | "function_call_output" | undefined;
    name?: string | undefined;
    status?: "in_progress" | "completed" | "incomplete" | undefined;
    output?: string | undefined;
    role?: "user" | "system" | "assistant" | undefined;
    content?: {
        type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
        text?: string | null | undefined;
        audio?: string | null | undefined;
        transcript?: string | null | undefined;
        id?: string | undefined;
    }[] | undefined;
    arguments?: string | undefined;
    id?: string | undefined;
    call_id?: string | undefined;
}, {
    object?: "realtime.item" | undefined;
    type?: "message" | "function_call" | "function_call_output" | undefined;
    name?: string | undefined;
    status?: "in_progress" | "completed" | "incomplete" | undefined;
    output?: string | undefined;
    role?: "user" | "system" | "assistant" | undefined;
    content?: {
        type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
        text?: string | null | undefined;
        audio?: string | null | undefined;
        transcript?: string | null | undefined;
        id?: string | undefined;
    }[] | undefined;
    arguments?: string | undefined;
    id?: string | undefined;
    call_id?: string | undefined;
}>;
export declare const conversationCreatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.created">;
    event_id: z.ZodString;
    conversation: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.conversation">>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    }, {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.created";
    event_id: string;
    conversation: {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    };
}, {
    type: "conversation.created";
    event_id: string;
    conversation: {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    };
}>;
export declare const conversationItemCreatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.created">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.created";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    previous_item_id?: string | null | undefined;
}, {
    type: "conversation.item.created";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    previous_item_id?: string | null | undefined;
}>;
export declare const conversationItemDeletedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.deleted">;
    event_id: z.ZodString;
    item_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.deleted";
    event_id: string;
    item_id: string;
}, {
    type: "conversation.item.deleted";
    event_id: string;
    item_id: string;
}>;
export declare const conversationItemInputAudioTranscriptionCompletedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.completed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    transcript: z.ZodString;
    logprobs: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodAny, "many">>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.completed";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    logprobs?: any[] | null | undefined;
}, {
    type: "conversation.item.input_audio_transcription.completed";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    logprobs?: any[] | null | undefined;
}>;
export declare const conversationItemInputAudioTranscriptionDeltaEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodOptional<z.ZodNumber>;
    delta: z.ZodOptional<z.ZodString>;
    logprobs: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodAny, "many">>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.delta";
    event_id: string;
    item_id: string;
    content_index?: number | undefined;
    logprobs?: any[] | null | undefined;
    delta?: string | undefined;
}, {
    type: "conversation.item.input_audio_transcription.delta";
    event_id: string;
    item_id: string;
    content_index?: number | undefined;
    logprobs?: any[] | null | undefined;
    delta?: string | undefined;
}>;
export declare const conversationItemInputAudioTranscriptionFailedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.failed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    error: z.ZodObject<{
        code: z.ZodOptional<z.ZodString>;
        message: z.ZodOptional<z.ZodString>;
        param: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    }, {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.failed";
    event_id: string;
    item_id: string;
    content_index: number;
    error: {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    };
}, {
    type: "conversation.item.input_audio_transcription.failed";
    event_id: string;
    item_id: string;
    content_index: number;
    error: {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    };
}>;
export declare const conversationItemRetrievedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.retrieved">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.retrieved";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
}, {
    type: "conversation.item.retrieved";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
}>;
export declare const conversationItemTruncatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.truncated">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
    content_index: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.truncated";
    event_id: string;
    item_id: string;
    content_index: number;
    audio_end_ms: number;
}, {
    type: "conversation.item.truncated";
    event_id: string;
    item_id: string;
    content_index: number;
    audio_end_ms: number;
}>;
export declare const conversationItemCreateEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.create">;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    event_id: z.ZodOptional<z.ZodString>;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.create";
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    event_id?: string | undefined;
    previous_item_id?: string | null | undefined;
}, {
    type: "conversation.item.create";
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    event_id?: string | undefined;
    previous_item_id?: string | null | undefined;
}>;
export declare const conversationItemDeleteEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.delete">;
    item_id: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.delete";
    item_id: string;
    event_id?: string | undefined;
}, {
    type: "conversation.item.delete";
    item_id: string;
    event_id?: string | undefined;
}>;
export declare const conversationItemRetrieveEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.retrieve">;
    item_id: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.retrieve";
    item_id: string;
    event_id?: string | undefined;
}, {
    type: "conversation.item.retrieve";
    item_id: string;
    event_id?: string | undefined;
}>;
export declare const conversationItemTruncateEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.truncate">;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
    content_index: z.ZodNumber;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.truncate";
    item_id: string;
    content_index: number;
    audio_end_ms: number;
    event_id?: string | undefined;
}, {
    type: "conversation.item.truncate";
    item_id: string;
    content_index: number;
    audio_end_ms: number;
    event_id?: string | undefined;
}>;
export declare const errorEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"error">;
    event_id: z.ZodOptional<z.ZodString>;
    error: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    type: "error";
    event_id?: string | undefined;
    error?: any;
}, {
    type: "error";
    event_id?: string | undefined;
    error?: any;
}>;
export declare const inputAudioBufferClearedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.cleared">;
    event_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.cleared";
    event_id: string;
}, {
    type: "input_audio_buffer.cleared";
    event_id: string;
}>;
export declare const inputAudioBufferAppendEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.append">;
    audio: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.append";
    audio: string;
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.append";
    audio: string;
    event_id?: string | undefined;
}>;
export declare const inputAudioBufferClearEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.clear">;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.clear";
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.clear";
    event_id?: string | undefined;
}>;
export declare const inputAudioBufferCommitEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.commit">;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.commit";
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.commit";
    event_id?: string | undefined;
}>;
export declare const inputAudioBufferCommittedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.committed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.committed";
    event_id: string;
    item_id: string;
    previous_item_id?: string | null | undefined;
}, {
    type: "input_audio_buffer.committed";
    event_id: string;
    item_id: string;
    previous_item_id?: string | null | undefined;
}>;
export declare const inputAudioBufferSpeechStartedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.speech_started">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_start_ms: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.speech_started";
    event_id: string;
    item_id: string;
    audio_start_ms: number;
}, {
    type: "input_audio_buffer.speech_started";
    event_id: string;
    item_id: string;
    audio_start_ms: number;
}>;
export declare const inputAudioBufferSpeechStoppedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.speech_stopped">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.speech_stopped";
    event_id: string;
    item_id: string;
    audio_end_ms: number;
}, {
    type: "input_audio_buffer.speech_stopped";
    event_id: string;
    item_id: string;
    audio_end_ms: number;
}>;
export declare const outputAudioBufferStartedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">>;
export declare const outputAudioBufferStoppedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">>;
export declare const outputAudioBufferClearedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.cleared">;
    event_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "output_audio_buffer.cleared";
    event_id: string;
}, {
    type: "output_audio_buffer.cleared";
    event_id: string;
}>;
export declare const rateLimitsUpdatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"rate_limits.updated">;
    event_id: z.ZodString;
    rate_limits: z.ZodArray<z.ZodObject<{
        limit: z.ZodOptional<z.ZodNumber>;
        name: z.ZodOptional<z.ZodEnum<["requests", "tokens"]>>;
        remaining: z.ZodOptional<z.ZodNumber>;
        reset_seconds: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }, {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    type: "rate_limits.updated";
    event_id: string;
    rate_limits: {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }[];
}, {
    type: "rate_limits.updated";
    event_id: string;
    rate_limits: {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }[];
}>;
export declare const responseAudioDeltaEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.audio.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>;
export declare const responseAudioDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.audio.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>;
export declare const responseAudioTranscriptDeltaEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.audio_transcript.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio_transcript.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio_transcript.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>;
export declare const responseAudioTranscriptDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.audio_transcript.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    transcript: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio_transcript.done";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio_transcript.done";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>;
export declare const responseContentPartAddedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.content_part.added">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
    part: z.ZodObject<{
        audio: z.ZodOptional<z.ZodString>;
        text: z.ZodOptional<z.ZodString>;
        transcript: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodEnum<["text", "audio"]>>;
    }, "strip", z.ZodTypeAny, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.content_part.added";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}, {
    type: "response.content_part.added";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}>;
export declare const responseContentPartDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.content_part.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
    part: z.ZodObject<{
        audio: z.ZodOptional<z.ZodString>;
        text: z.ZodOptional<z.ZodString>;
        transcript: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodEnum<["text", "audio"]>>;
    }, "strip", z.ZodTypeAny, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.content_part.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}, {
    type: "response.content_part.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}>;
export declare const responseCreatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.created">;
    event_id: z.ZodString;
    response: z.ZodObject<{
        id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        conversation_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        max_output_tokens: z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodLiteral<"inf">]>>>;
        metadata: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        modalities: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
        object: z.ZodNullable<z.ZodOptional<z.ZodLiteral<"realtime.response">>>;
        output: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodAny, "many">>>;
        output_audio_format: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        status: z.ZodNullable<z.ZodOptional<z.ZodEnum<["completed", "incomplete", "failed", "cancelled", "in_progress"]>>>;
        status_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        usage: z.ZodNullable<z.ZodOptional<z.ZodObject<{
            input_tokens: z.ZodOptional<z.ZodNumber>;
            input_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
            output_tokens: z.ZodOptional<z.ZodNumber>;
            output_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        }, "strip", z.ZodTypeAny, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }>>>;
        voice: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.created";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}, {
    type: "response.created";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}>;
export declare const responseDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.done">;
    event_id: z.ZodString;
    response: z.ZodObject<{
        id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        conversation_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        max_output_tokens: z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodLiteral<"inf">]>>>;
        metadata: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        modalities: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
        object: z.ZodNullable<z.ZodOptional<z.ZodLiteral<"realtime.response">>>;
        output: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodAny, "many">>>;
        output_audio_format: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        status: z.ZodNullable<z.ZodOptional<z.ZodEnum<["completed", "incomplete", "failed", "cancelled", "in_progress"]>>>;
        status_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        usage: z.ZodNullable<z.ZodOptional<z.ZodObject<{
            input_tokens: z.ZodOptional<z.ZodNumber>;
            input_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
            output_tokens: z.ZodOptional<z.ZodNumber>;
            output_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        }, "strip", z.ZodTypeAny, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }>>>;
        voice: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.done";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}, {
    type: "response.done";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}>;
export declare const responseFunctionCallArgumentsDeltaEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.function_call_arguments.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    call_id: z.ZodString;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.function_call_arguments.delta";
    call_id: string;
    event_id: string;
    item_id: string;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.function_call_arguments.delta";
    call_id: string;
    event_id: string;
    item_id: string;
    delta: string;
    output_index: number;
    response_id: string;
}>;
export declare const responseFunctionCallArgumentsDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.function_call_arguments.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    call_id: z.ZodString;
    arguments: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.function_call_arguments.done";
    arguments: string;
    call_id: string;
    event_id: string;
    item_id: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.function_call_arguments.done";
    arguments: string;
    call_id: string;
    event_id: string;
    item_id: string;
    output_index: number;
    response_id: string;
}>;
export declare const responseOutputItemAddedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.output_item.added">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.output_item.added";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}, {
    type: "response.output_item.added";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}>;
export declare const responseOutputItemDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.output_item.done">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.output_item.done";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}, {
    type: "response.output_item.done";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}>;
export declare const responseTextDeltaEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.text.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.text.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.text.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>;
export declare const responseTextDoneEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.text.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    text: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.text.done";
    text: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.text.done";
    text: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>;
export declare const sessionCreatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"session.created">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.created";
    event_id: string;
    session?: any;
}, {
    type: "session.created";
    event_id: string;
    session?: any;
}>;
export declare const sessionUpdatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"session.updated">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.updated";
    event_id: string;
    session?: any;
}, {
    type: "session.updated";
    event_id: string;
    session?: any;
}>;
export declare const responseCancelEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.cancel">;
    event_id: z.ZodOptional<z.ZodString>;
    response_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "response.cancel";
    event_id?: string | undefined;
    response_id?: string | undefined;
}, {
    type: "response.cancel";
    event_id?: string | undefined;
    response_id?: string | undefined;
}>;
export declare const responseCreateEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"response.create">;
    event_id: z.ZodOptional<z.ZodString>;
    response: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    type: "response.create";
    event_id?: string | undefined;
    response?: any;
}, {
    type: "response.create";
    event_id?: string | undefined;
    response?: any;
}>;
export declare const sessionUpdateEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"session.update">;
    event_id: z.ZodOptional<z.ZodString>;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.update";
    event_id?: string | undefined;
    session?: any;
}, {
    type: "session.update";
    event_id?: string | undefined;
    session?: any;
}>;
export declare const transcriptionSessionUpdateEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"transcription_session.update">;
    event_id: z.ZodOptional<z.ZodString>;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "transcription_session.update";
    event_id?: string | undefined;
    session?: any;
}, {
    type: "transcription_session.update";
    event_id?: string | undefined;
    session?: any;
}>;
export declare const transcriptionSessionUpdatedEventSchema: z.ZodObject<{
    type: z.ZodLiteral<"transcription_session.updated">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "transcription_session.updated";
    event_id: string;
    session?: any;
}, {
    type: "transcription_session.updated";
    event_id: string;
    session?: any;
}>;
/**
 * This schema is used if an event is unknown to the client. The Realtime API might introduce
 * new events at some point and we should handle them gracefully by treating them as generic events
 * only requiring a type and an optional event_id.
 */
export declare const genericEventSchema: z.ZodObject<{
    type: z.ZodString;
    event_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    type: z.ZodString;
    event_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    type: z.ZodString;
    event_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
}, z.ZodTypeAny, "passthrough">>;
export declare const realtimeServerEventSchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodLiteral<"conversation.created">;
    event_id: z.ZodString;
    conversation: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.conversation">>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    }, {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.created";
    event_id: string;
    conversation: {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    };
}, {
    type: "conversation.created";
    event_id: string;
    conversation: {
        object?: "realtime.conversation" | undefined;
        id?: string | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.created">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.created";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    previous_item_id?: string | null | undefined;
}, {
    type: "conversation.item.created";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    previous_item_id?: string | null | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.deleted">;
    event_id: z.ZodString;
    item_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.deleted";
    event_id: string;
    item_id: string;
}, {
    type: "conversation.item.deleted";
    event_id: string;
    item_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.completed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    transcript: z.ZodString;
    logprobs: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodAny, "many">>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.completed";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    logprobs?: any[] | null | undefined;
}, {
    type: "conversation.item.input_audio_transcription.completed";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    logprobs?: any[] | null | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodOptional<z.ZodNumber>;
    delta: z.ZodOptional<z.ZodString>;
    logprobs: z.ZodOptional<z.ZodNullable<z.ZodArray<z.ZodAny, "many">>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.delta";
    event_id: string;
    item_id: string;
    content_index?: number | undefined;
    logprobs?: any[] | null | undefined;
    delta?: string | undefined;
}, {
    type: "conversation.item.input_audio_transcription.delta";
    event_id: string;
    item_id: string;
    content_index?: number | undefined;
    logprobs?: any[] | null | undefined;
    delta?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.input_audio_transcription.failed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    error: z.ZodObject<{
        code: z.ZodOptional<z.ZodString>;
        message: z.ZodOptional<z.ZodString>;
        param: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    }, {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.input_audio_transcription.failed";
    event_id: string;
    item_id: string;
    content_index: number;
    error: {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    };
}, {
    type: "conversation.item.input_audio_transcription.failed";
    event_id: string;
    item_id: string;
    content_index: number;
    error: {
        type?: string | undefined;
        message?: string | undefined;
        code?: string | undefined;
        param?: string | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.retrieved">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.retrieved";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
}, {
    type: "conversation.item.retrieved";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.truncated">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
    content_index: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.truncated";
    event_id: string;
    item_id: string;
    content_index: number;
    audio_end_ms: number;
}, {
    type: "conversation.item.truncated";
    event_id: string;
    item_id: string;
    content_index: number;
    audio_end_ms: number;
}>, z.ZodObject<{
    type: z.ZodLiteral<"error">;
    event_id: z.ZodOptional<z.ZodString>;
    error: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    type: "error";
    event_id?: string | undefined;
    error?: any;
}, {
    type: "error";
    event_id?: string | undefined;
    error?: any;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.cleared">;
    event_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.cleared";
    event_id: string;
}, {
    type: "input_audio_buffer.cleared";
    event_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.committed">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.committed";
    event_id: string;
    item_id: string;
    previous_item_id?: string | null | undefined;
}, {
    type: "input_audio_buffer.committed";
    event_id: string;
    item_id: string;
    previous_item_id?: string | null | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.speech_started">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_start_ms: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.speech_started";
    event_id: string;
    item_id: string;
    audio_start_ms: number;
}, {
    type: "input_audio_buffer.speech_started";
    event_id: string;
    item_id: string;
    audio_start_ms: number;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.speech_stopped">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.speech_stopped";
    event_id: string;
    item_id: string;
    audio_end_ms: number;
}, {
    type: "input_audio_buffer.speech_stopped";
    event_id: string;
    item_id: string;
    audio_end_ms: number;
}>, z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    type: z.ZodLiteral<"output_audio_buffer.started">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">>, z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    type: z.ZodLiteral<"output_audio_buffer.stopped">;
    event_id: z.ZodString;
}, z.ZodTypeAny, "passthrough">>, z.ZodObject<{
    type: z.ZodLiteral<"output_audio_buffer.cleared">;
    event_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "output_audio_buffer.cleared";
    event_id: string;
}, {
    type: "output_audio_buffer.cleared";
    event_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"rate_limits.updated">;
    event_id: z.ZodString;
    rate_limits: z.ZodArray<z.ZodObject<{
        limit: z.ZodOptional<z.ZodNumber>;
        name: z.ZodOptional<z.ZodEnum<["requests", "tokens"]>>;
        remaining: z.ZodOptional<z.ZodNumber>;
        reset_seconds: z.ZodOptional<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }, {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    type: "rate_limits.updated";
    event_id: string;
    rate_limits: {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }[];
}, {
    type: "rate_limits.updated";
    event_id: string;
    rate_limits: {
        name?: "requests" | "tokens" | undefined;
        limit?: number | undefined;
        remaining?: number | undefined;
        reset_seconds?: number | undefined;
    }[];
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.audio.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.audio.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.audio_transcript.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio_transcript.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio_transcript.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.audio_transcript.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    transcript: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.audio_transcript.done";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.audio_transcript.done";
    transcript: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.content_part.added">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
    part: z.ZodObject<{
        audio: z.ZodOptional<z.ZodString>;
        text: z.ZodOptional<z.ZodString>;
        transcript: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodEnum<["text", "audio"]>>;
    }, "strip", z.ZodTypeAny, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.content_part.added";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}, {
    type: "response.content_part.added";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.content_part.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
    part: z.ZodObject<{
        audio: z.ZodOptional<z.ZodString>;
        text: z.ZodOptional<z.ZodString>;
        transcript: z.ZodOptional<z.ZodString>;
        type: z.ZodOptional<z.ZodEnum<["text", "audio"]>>;
    }, "strip", z.ZodTypeAny, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }, {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.content_part.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}, {
    type: "response.content_part.done";
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
    part: {
        type?: "text" | "audio" | undefined;
        text?: string | undefined;
        audio?: string | undefined;
        transcript?: string | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.created">;
    event_id: z.ZodString;
    response: z.ZodObject<{
        id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        conversation_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        max_output_tokens: z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodLiteral<"inf">]>>>;
        metadata: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        modalities: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
        object: z.ZodNullable<z.ZodOptional<z.ZodLiteral<"realtime.response">>>;
        output: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodAny, "many">>>;
        output_audio_format: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        status: z.ZodNullable<z.ZodOptional<z.ZodEnum<["completed", "incomplete", "failed", "cancelled", "in_progress"]>>>;
        status_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        usage: z.ZodNullable<z.ZodOptional<z.ZodObject<{
            input_tokens: z.ZodOptional<z.ZodNumber>;
            input_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
            output_tokens: z.ZodOptional<z.ZodNumber>;
            output_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        }, "strip", z.ZodTypeAny, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }>>>;
        voice: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.created";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}, {
    type: "response.created";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.done">;
    event_id: z.ZodString;
    response: z.ZodObject<{
        id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        conversation_id: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        max_output_tokens: z.ZodNullable<z.ZodOptional<z.ZodUnion<[z.ZodNumber, z.ZodLiteral<"inf">]>>>;
        metadata: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        modalities: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodString, "many">>>;
        object: z.ZodNullable<z.ZodOptional<z.ZodLiteral<"realtime.response">>>;
        output: z.ZodNullable<z.ZodOptional<z.ZodArray<z.ZodAny, "many">>>;
        output_audio_format: z.ZodNullable<z.ZodOptional<z.ZodString>>;
        status: z.ZodNullable<z.ZodOptional<z.ZodEnum<["completed", "incomplete", "failed", "cancelled", "in_progress"]>>>;
        status_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        usage: z.ZodNullable<z.ZodOptional<z.ZodObject<{
            input_tokens: z.ZodOptional<z.ZodNumber>;
            input_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
            output_tokens: z.ZodOptional<z.ZodNumber>;
            output_tokens_details: z.ZodNullable<z.ZodOptional<z.ZodRecord<z.ZodString, z.ZodAny>>>;
        }, "strip", z.ZodTypeAny, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }, {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        }>>>;
        voice: z.ZodNullable<z.ZodOptional<z.ZodString>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }, {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    type: "response.done";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}, {
    type: "response.done";
    event_id: string;
    response: {
        object?: "realtime.response" | null | undefined;
        status?: "in_progress" | "completed" | "incomplete" | "failed" | "cancelled" | null | undefined;
        output?: any[] | null | undefined;
        id?: string | null | undefined;
        conversation_id?: string | null | undefined;
        max_output_tokens?: number | "inf" | null | undefined;
        metadata?: Record<string, any> | null | undefined;
        modalities?: string[] | null | undefined;
        output_audio_format?: string | null | undefined;
        status_details?: Record<string, any> | null | undefined;
        usage?: {
            input_tokens?: number | undefined;
            input_tokens_details?: Record<string, any> | null | undefined;
            output_tokens?: number | undefined;
            output_tokens_details?: Record<string, any> | null | undefined;
        } | null | undefined;
        voice?: string | null | undefined;
    };
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.function_call_arguments.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    call_id: z.ZodString;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.function_call_arguments.delta";
    call_id: string;
    event_id: string;
    item_id: string;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.function_call_arguments.delta";
    call_id: string;
    event_id: string;
    item_id: string;
    delta: string;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.function_call_arguments.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    call_id: z.ZodString;
    arguments: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.function_call_arguments.done";
    arguments: string;
    call_id: string;
    event_id: string;
    item_id: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.function_call_arguments.done";
    arguments: string;
    call_id: string;
    event_id: string;
    item_id: string;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.output_item.added">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.output_item.added";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}, {
    type: "response.output_item.added";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.output_item.done">;
    event_id: z.ZodString;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.output_item.done";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}, {
    type: "response.output_item.done";
    event_id: string;
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.text.delta">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    delta: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.text.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}, {
    type: "response.text.delta";
    event_id: string;
    item_id: string;
    content_index: number;
    delta: string;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.text.done">;
    event_id: z.ZodString;
    item_id: z.ZodString;
    content_index: z.ZodNumber;
    text: z.ZodString;
    output_index: z.ZodNumber;
    response_id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    type: "response.text.done";
    text: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}, {
    type: "response.text.done";
    text: string;
    event_id: string;
    item_id: string;
    content_index: number;
    output_index: number;
    response_id: string;
}>, z.ZodObject<{
    type: z.ZodLiteral<"session.created">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.created";
    event_id: string;
    session?: any;
}, {
    type: "session.created";
    event_id: string;
    session?: any;
}>, z.ZodObject<{
    type: z.ZodLiteral<"session.updated">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.updated";
    event_id: string;
    session?: any;
}, {
    type: "session.updated";
    event_id: string;
    session?: any;
}>, z.ZodObject<{
    type: z.ZodLiteral<"transcription_session.updated">;
    event_id: z.ZodString;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "transcription_session.updated";
    event_id: string;
    session?: any;
}, {
    type: "transcription_session.updated";
    event_id: string;
    session?: any;
}>]>;
export declare const realtimeClientEventSchema: z.ZodDiscriminatedUnion<"type", [z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.create">;
    item: z.ZodObject<{
        id: z.ZodOptional<z.ZodString>;
        arguments: z.ZodOptional<z.ZodString>;
        call_id: z.ZodOptional<z.ZodString>;
        content: z.ZodOptional<z.ZodArray<z.ZodObject<{
            id: z.ZodOptional<z.ZodString>;
            audio: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            text: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            transcript: z.ZodOptional<z.ZodNullable<z.ZodString>>;
            type: z.ZodUnion<[z.ZodLiteral<"input_text">, z.ZodLiteral<"input_audio">, z.ZodLiteral<"item_reference">, z.ZodLiteral<"text">, z.ZodLiteral<"audio">]>;
        }, "strip", z.ZodTypeAny, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }, {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }>, "many">>;
        name: z.ZodOptional<z.ZodString>;
        object: z.ZodOptional<z.ZodLiteral<"realtime.item">>;
        output: z.ZodOptional<z.ZodString>;
        role: z.ZodOptional<z.ZodEnum<["user", "assistant", "system"]>>;
        status: z.ZodOptional<z.ZodEnum<["completed", "incomplete", "in_progress"]>>;
        type: z.ZodOptional<z.ZodEnum<["message", "function_call", "function_call_output"]>>;
    }, "strip", z.ZodTypeAny, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }, {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    }>;
    event_id: z.ZodOptional<z.ZodString>;
    previous_item_id: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.create";
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    event_id?: string | undefined;
    previous_item_id?: string | null | undefined;
}, {
    type: "conversation.item.create";
    item: {
        object?: "realtime.item" | undefined;
        type?: "message" | "function_call" | "function_call_output" | undefined;
        name?: string | undefined;
        status?: "in_progress" | "completed" | "incomplete" | undefined;
        output?: string | undefined;
        role?: "user" | "system" | "assistant" | undefined;
        content?: {
            type: "input_text" | "text" | "audio" | "input_audio" | "item_reference";
            text?: string | null | undefined;
            audio?: string | null | undefined;
            transcript?: string | null | undefined;
            id?: string | undefined;
        }[] | undefined;
        arguments?: string | undefined;
        id?: string | undefined;
        call_id?: string | undefined;
    };
    event_id?: string | undefined;
    previous_item_id?: string | null | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.delete">;
    item_id: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.delete";
    item_id: string;
    event_id?: string | undefined;
}, {
    type: "conversation.item.delete";
    item_id: string;
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.retrieve">;
    item_id: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.retrieve";
    item_id: string;
    event_id?: string | undefined;
}, {
    type: "conversation.item.retrieve";
    item_id: string;
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"conversation.item.truncate">;
    item_id: z.ZodString;
    audio_end_ms: z.ZodNumber;
    content_index: z.ZodNumber;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "conversation.item.truncate";
    item_id: string;
    content_index: number;
    audio_end_ms: number;
    event_id?: string | undefined;
}, {
    type: "conversation.item.truncate";
    item_id: string;
    content_index: number;
    audio_end_ms: number;
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.append">;
    audio: z.ZodString;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.append";
    audio: string;
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.append";
    audio: string;
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.clear">;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.clear";
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.clear";
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"input_audio_buffer.commit">;
    event_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "input_audio_buffer.commit";
    event_id?: string | undefined;
}, {
    type: "input_audio_buffer.commit";
    event_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.cancel">;
    event_id: z.ZodOptional<z.ZodString>;
    response_id: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    type: "response.cancel";
    event_id?: string | undefined;
    response_id?: string | undefined;
}, {
    type: "response.cancel";
    event_id?: string | undefined;
    response_id?: string | undefined;
}>, z.ZodObject<{
    type: z.ZodLiteral<"response.create">;
    event_id: z.ZodOptional<z.ZodString>;
    response: z.ZodOptional<z.ZodAny>;
}, "strip", z.ZodTypeAny, {
    type: "response.create";
    event_id?: string | undefined;
    response?: any;
}, {
    type: "response.create";
    event_id?: string | undefined;
    response?: any;
}>, z.ZodObject<{
    type: z.ZodLiteral<"session.update">;
    event_id: z.ZodOptional<z.ZodString>;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "session.update";
    event_id?: string | undefined;
    session?: any;
}, {
    type: "session.update";
    event_id?: string | undefined;
    session?: any;
}>, z.ZodObject<{
    type: z.ZodLiteral<"transcription_session.update">;
    event_id: z.ZodOptional<z.ZodString>;
    session: z.ZodAny;
}, "strip", z.ZodTypeAny, {
    type: "transcription_session.update";
    event_id?: string | undefined;
    session?: any;
}, {
    type: "transcription_session.update";
    event_id?: string | undefined;
    session?: any;
}>]>;
export type RealtimeServerGenericEvent = z.infer<typeof genericEventSchema> & Record<string, any>;
export type RealtimeServerEvent = z.infer<typeof realtimeServerEventSchema> & Record<string, any>;
export type RealtimeClientEvent = z.infer<typeof realtimeClientEventSchema> & Record<string, any>;
type ParseResult = {
    data: RealtimeServerEvent;
    isGeneric: false;
} | {
    data: RealtimeServerGenericEvent;
    isGeneric: true;
} | {
    data: null;
    isGeneric: true;
};
/**
 * Parses a realtime event from the server. If the event is unknown to the client, it will be treated as a generic event.
 * @param event - The event to parse.
 * @returns The parsed event or null if the event is unknown to the client.
 */
export declare function parseRealtimeEvent(event: MessageEvent | WebSocketMessageEvent): ParseResult;
export {};
