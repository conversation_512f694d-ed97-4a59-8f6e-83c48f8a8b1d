{"version": 3, "file": "openaiRealtimeWebsocket.js", "sourceRoot": "", "sources": ["../src/openaiRealtimeWebsocket.ts"], "names": [], "mappings": "OAAO,EACL,oBAAoB,EACpB,SAAS,GACV,MAAM,gCAAgC;OAOhC,EACL,kBAAkB,GAEnB;OACM,EAAE,mBAAmB,EAAE,OAAO,EAAE,cAAc,EAAE;OAChD,EAAE,SAAS,EAAE,MAAM,qBAAqB;OAExC,EAAE,kBAAkB,EAAE;AAiC7B;;;;GAIG;AACH,MAAM,OAAO,uBACX,SAAQ,kBAAkB;IAG1B,OAAO,CAAqB;IAC5B,IAAI,CAAS;IACb,MAAM,GAAmB;QACvB,MAAM,EAAE,cAAc;QACtB,SAAS,EAAE,SAAS;KACrB,CAAC;IACF,kBAAkB,CAAU;IAC5B,cAAc,CAAqB;IACnC,yBAAyB,CAAqB;IAC9C;;;;;;OAMG;IACO,oBAAoB,CAAqB;IACzC,cAAc,GAAW,CAAC,CAAC;IACrC,gBAAgB,GAAY,KAAK,CAAC;IAElC,YAAY,UAA0C,EAAE;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,0CAA0C,IAAI,CAAC,YAAY,EAAE,CAAC;QAC1E,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,IAAI,KAAK,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,IAAc,aAAa;QACzB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACO,QAAQ,CAAC,UAA+B;QAChD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,eAAe,CACb,OAA8B,EAC9B,MAA8B,EAC9B,aAA6C;QAE7C,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC1B,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,SAAS,CACjB,oEAAoE,CACrE,CAAC;QACJ,CAAC;QAED,IACE,oBAAoB,EAAE;YACtB,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC;YAC/B,CAAC,IAAI,CAAC,kBAAkB,EACxB,CAAC;YACD,MAAM,IAAI,SAAS,CACjB,6KAA6K,CAC9K,CAAC;QACJ,CAAC;QAED,MAAM,kBAAkB,GAAG,oBAAoB,EAAE;YAC/C,CAAC,CAAC;gBACE,UAAU;gBACV,OAAO;gBACP,0BAA0B,GAAG,IAAI,CAAC,OAAO;gBACzC,0BAA0B;gBAC1B,yBAAyB;gBACzB,iBAAiB;gBACjB,cAAc;aACf;YACH,CAAC,CAAC;gBACE,OAAO,EAAE;oBACP,aAAa,EAAE,UAAU,IAAI,CAAC,OAAO,EAAE;oBACvC,aAAa,EAAE,aAAa;oBAC5B,GAAG,OAAO;iBACX;aACF,CAAC;QAEN,MAAM,EAAE,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,kBAAyB,CAAC,CAAC;QAC/D,IAAI,CAAC,MAAM,GAAG;YACZ,MAAM,EAAE,YAAY;YACpB,SAAS,EAAE,EAAE;SACd,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEnD,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE;YAC/B,IAAI,CAAC,MAAM,GAAG;gBACZ,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,EAAE;aACd,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,OAAO,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrB,IAAI,CAAC,MAAM,GAAG;gBACZ,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,SAAS;aACrB,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,EAAE;YACzC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,IAAI,SAAS,EAAE,CAAC;gBACzB,OAAO;YACT,CAAC;YAED,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;gBAC3C,IAAI,CAAC,yBAAyB,GAAG,MAAM,CAAC,aAAa,CAAC;gBACtD,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC;gBACrC,IAAI,IAAI,CAAC,oBAAoB,KAAK,SAAS,EAAE,CAAC;oBAC5C,6EAA6E;oBAC7E,+DAA+D;oBAC/D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;oBACvC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;gBAC1B,CAAC;gBAED,MAAM,IAAI,GAAG,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC/C,oEAAoE;gBACpE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,6BAA6B;gBAE9E,MAAM,UAAU,GAAwB;oBACtC,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,IAAI;oBACV,UAAU,EAAE,MAAM,CAAC,WAAW;iBAC/B,CAAC;gBACF,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAC5B,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,mCAAmC,EAAE,CAAC;gBAC/D,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBAC9C,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;YAC/B,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBAC3C,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAChC,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBAC7C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBAC7C,yDAAyD;gBACzD,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAChC,IAAI,CAAC,MAAM,GAAG;gBACZ,MAAM,EAAE,cAAc;gBACtB,SAAS,EAAE,SAAS;aACrB,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAA6C;QACzD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC;QACjD,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,CAAC,IAAI;YACP,OAAO,CAAC,GAAG;gBACX,0CAA0C,IAAI,CAAC,YAAY,EAAE,CAAC;QAEhE,MAAM,aAAa,GAAmC;YACpD,GAAG,CAAC,OAAO,CAAC,oBAAoB,IAAI,EAAE,CAAC;YACvC,KAAK,EAAE,IAAI,CAAC,YAAY;SACzB,CAAC;QAEF,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC1C,IAAI,CAAC;gBACH,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;YACvD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;IAChD,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAC,KAA4B;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CACb,mFAAmF,CACpF,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACH,KAAK;QACH,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,IAAI,CAAC,MAAe;QAClB,MAAM,IAAI,KAAK,CACb,+FAA+F,CAChG,CAAC;IACJ,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAC,KAAkB,EAAE,UAAgC,EAAE;QAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACvC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,8BAA8B;QAC9B,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC;gBACb,IAAI,EAAE,iBAAiB;aACxB,CAAC,CAAC;YACH,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAChC,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACH,UAAU,CAAC,WAAmB;QAC5B,mEAAmE;QACnE,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,4BAA4B;YAClC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,aAAa,EAAE,IAAI,CAAC,yBAAyB;YAC7C,YAAY,EAAE,WAAW;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,SAAS;QACP,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,OAAO,IAAI,CAAC,oBAAoB,KAAK,QAAQ,EAAE,CAAC;YAC1E,OAAO;QACT,CAAC;QAED,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC3D,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC1D,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC/B,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC;QACtC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,yBAAyB,GAAG,SAAS,CAAC;IAC7C,CAAC;CACF"}