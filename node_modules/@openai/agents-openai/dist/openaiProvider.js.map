{"version": 3, "file": "openaiProvider.js", "sourceRoot": "", "sources": ["../src/openaiProvider.ts"], "names": [], "mappings": "OACO,MAAM,MAAM,QAAQ;OACpB,EACL,oBAAoB,EACpB,sBAAsB,EACtB,mBAAmB,EACnB,2BAA2B,GAC5B;OACM,EAAE,oBAAoB,EAAE;OACxB,EAAE,0BAA0B,EAAE;AAcrC;;GAEG;AACH,MAAM,OAAO,cAAc;IACzB,OAAO,CAAU;IACjB,aAAa,CAAW;IACxB,QAAQ,CAAwB;IAEhC,YAAY,UAAiC,EAAE;QAC7C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;YAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACjE,CAAC;YACD,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBAC1B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAClE,CAAC;YACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;QAC5C,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC;IAClD,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO;gBACV,6DAA6D;gBAC7D,sBAAsB,EAAE;oBACxB,uCAAuC;oBACvC,IAAI,MAAM,CAAC;wBACT,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,mBAAmB,EAAE;wBACrD,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;wBAC9B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;wBACxC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;qBAC/B,CAAC,CAAC;QACP,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,SAA8B;QAC3C,MAAM,KAAK,GAAG,SAAS,IAAI,oBAAoB,CAAC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,IAAI,2BAA2B,EAAE,CAAC;QAEzE,IAAI,YAAY,EAAE,CAAC;YACjB,OAAO,IAAI,oBAAoB,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,IAAI,0BAA0B,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,CAAC,CAAC;IAClE,CAAC;CACF"}