import { OpenAI } from 'openai';
export declare const DEFAULT_OPENAI_API = "responses";
export declare const DEFAULT_OPENAI_MODEL = "gpt-4.1";
export declare function setTracingExportApiKey(key: string): void;
export declare function getTracingExportApiKey(): string | undefined;
export declare function shouldUseResponsesByDefault(): boolean;
export declare function setOpenAIAPI(value: 'chat_completions' | 'responses'): void;
export declare function setDefaultOpenAIClient(client: OpenAI): void;
export declare function getDefaultOpenAIClient(): OpenAI | undefined;
export declare function setDefaultOpenAIKey(key: string): void;
export declare function getDefaultOpenAIKey(): string | undefined;
export declare const HEADERS: {
    'User-Agent': string;
};
