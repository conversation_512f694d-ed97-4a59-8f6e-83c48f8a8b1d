{"version": 3, "file": "openaiTracingExporter.mjs", "sourceRoot": "", "sources": ["../src/openaiTracingExporter.ts"], "names": [], "mappings": "OAAO,EAEL,mBAAmB,EACnB,kBAAkB,GACnB,MAAM,qBAAqB;OAGrB,EAAE,sBAAsB,EAAE,OAAO,EAAE;OACnC,MAAM;AAeb;;GAEG;AACH,MAAM,OAAO,qBAAqB;IAChC,QAAQ,CAA+B;IAEvC,YAAY,UAAiD,EAAE;QAC7D,IAAI,CAAC,QAAQ,GAAG;YACd,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,SAAS;YACnC,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE;YACxC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,yCAAyC;YACvE,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC;YACnC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;YACpC,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,KAAK;SACpC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,MAAM,CACV,KAA4B,EAC5B,MAAoB;QAEpB,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,sBAAsB,EAAE,CAAC;QAChE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,KAAK,CACV,0EAA0E,CAC3E,CAAC;YACF,OAAO;QACT,CAAC;QAED,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;SACpE,CAAC;QAEF,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;QAEpC,OAAO,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;oBACnD,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE;wBACP,cAAc,EAAE,kBAAkB;wBAClC,aAAa,EAAE,UAAU,MAAM,EAAE;wBACjC,aAAa,EAAE,WAAW;wBAC1B,GAAG,OAAO;qBACX;oBACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;oBAC7B,MAAM;iBACP,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;oBAChB,MAAM,CAAC,KAAK,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,CAAC;oBACtD,OAAO;gBACT,CAAC;gBAED,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBACpD,MAAM,CAAC,KAAK,CACV,oCACE,QAAQ,CAAC,MACX,KAAK,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,CAC7B,CAAC;oBACF,OAAO;gBACT,CAAC;gBAED,MAAM,CAAC,IAAI,CACT,qCAAqC,QAAQ,CAAC,MAAM,aAAa,CAClE,CAAC;YACJ,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,MAAM,EAAE,OAAO,EAAE,CAAC;gBACpB,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBACzC,OAAO;YACT,CAAC;YAED,MAAM,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,aAAa;YACpE,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;YAC/D,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACpD,QAAQ,EAAE,CAAC;QACb,CAAC;QAED,MAAM,CAAC,KAAK,CACV,0CACE,IAAI,CAAC,QAAQ,CAAC,UAChB,WAAW,CACZ,CAAC;IACJ,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,UAAU,+BAA+B;IAC7C,MAAM,QAAQ,GAAG,IAAI,qBAAqB,EAAE,CAAC;IAC7C,MAAM,SAAS,GAAG,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IACpD,kBAAkB,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AAClC,CAAC"}