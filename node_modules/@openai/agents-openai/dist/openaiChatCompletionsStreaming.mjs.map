{"version": 3, "file": "openaiChatCompletionsStreaming.mjs", "sourceRoot": "", "sources": ["../src/openaiChatCompletionsStreaming.ts"], "names": [], "mappings": "OAIO,EAAE,OAAO,EAAE;AASlB,MAAM,CAAC,KAAK,SAAS,CAAC,CAAC,uCAAuC,CAC5D,QAAwB,EACxB,MAAmC;IAEnC,IAAI,KAAK,GAAgC,SAAS,CAAC;IACnD,MAAM,KAAK,GAAmB;QAC5B,OAAO,EAAE,KAAK;QACd,6BAA6B,EAAE,IAAI;QACnC,gCAAgC,EAAE,IAAI;QACtC,cAAc,EAAE,EAAE;KACnB,CAAC;IAEF,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACnB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;YACrB,MAAM;gBACJ,IAAI,EAAE,kBAAkB;gBACxB,YAAY,EAAE;oBACZ,GAAG,KAAK;iBACT;aACF,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,MAAM;YACJ,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,KAAK;SACb,CAAC;QAEF,uEAAuE;QACvE,KAAK,GAAI,KAAa,CAAC,KAAK,IAAI,SAAS,CAAC;QAE1C,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK;YAAE,SAAS;QACzC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAErC,cAAc;QACd,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,CAAC;gBACzC,KAAK,CAAC,6BAA6B,GAAG;oBACpC,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC/C,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,YAAY,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE;iBACrE,CAAC;YACJ,CAAC;YACD,MAAM;gBACJ,IAAI,EAAE,mBAAmB;gBACzB,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,YAAY,EAAE;oBACZ,GAAG,KAAK;iBACT;aACF,CAAC;YACF,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC;QAC/D,CAAC;QAED,kBAAkB;QAClB,IAAI,SAAS,IAAI,KAAK,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACxC,IAAI,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC;gBAC5C,KAAK,CAAC,gCAAgC,GAAG;oBACvC,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5C,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBACjC,CAAC;YACJ,CAAC;YACD,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QACrE,CAAC;QAED,oBAAoB;QACpB,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;gBACxC,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;oBAC9C,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG;wBACrC,EAAE,EAAE,OAAO;wBACX,SAAS,EAAE,EAAE;wBACb,IAAI,EAAE,EAAE;wBACR,IAAI,EAAE,eAAe;wBACrB,MAAM,EAAE,EAAE;qBACX,CAAC;gBACJ,CAAC;gBACD,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBACtC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,SAAS;oBAC5C,WAAW,EAAE,SAAS,IAAI,EAAE,CAAC;gBAC/B,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC;gBACrE,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,MAAM,IAAI,QAAQ,CAAC,EAAE,IAAI,EAAE,CAAC;YACnE,CAAC;QACH,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,MAAM,OAAO,GAA+B,EAAE,CAAC;IAC/C,IACE,KAAK,CAAC,6BAA6B;QACnC,KAAK,CAAC,gCAAgC,EACtC,CAAC;QACD,MAAM,aAAa,GAAkC;YACnD,EAAE,EAAE,OAAO;YACX,OAAO,EAAE,EAAE;YACX,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,WAAW;SACpB,CAAC;QACF,IAAI,KAAK,CAAC,6BAA6B,EAAE,CAAC;YACxC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,KAAK,CAAC,gCAAgC,EAAE,CAAC;YAC3C,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxE,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9B,CAAC;IAED,KAAK,MAAM,aAAa,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAC9B,CAAC;IAED,yBAAyB;IACzB,MAAM,UAAU,GAA0C;QACxD,IAAI,EAAE,eAAe;QACrB,QAAQ,EAAE;YACR,EAAE,EAAE,QAAQ,CAAC,EAAE;YACf,KAAK,EAAE;gBACL,WAAW,EAAE,KAAK,EAAE,aAAa,IAAI,CAAC;gBACtC,YAAY,EAAE,KAAK,EAAE,iBAAiB,IAAI,CAAC;gBAC3C,WAAW,EAAE,KAAK,EAAE,YAAY,IAAI,CAAC;gBACrC,kBAAkB,EAAE;oBAClB,aAAa,EAAE,KAAK,EAAE,qBAAqB,EAAE,aAAa,IAAI,CAAC;iBAChE;gBACD,mBAAmB,EAAE;oBACnB,gBAAgB,EACb,KAAa,EAAE,yBAAyB,EAAE,gBAAgB,IAAI,CAAC;iBACnE;aACF;YACD,MAAM,EAAE,OAAO;SAChB;KACF,CAAC;IAEF,MAAM,UAAU,CAAC;AACnB,CAAC"}