{"version": 3, "file": "openaiChatCompletionsConverter.js", "sourceRoot": "", "sources": ["../src/openaiChatCompletionsConverter.ts"], "names": [], "mappings": "OAOO,EAKL,SAAS,GACV,MAAM,qBAAqB;AAE5B,MAAM,UAAU,iBAAiB,CAC/B,UAAoE;IAEpE,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,IAAI,IAAI;QAAE,OAAO,SAAS,CAAC;IACpE,IACE,UAAU,KAAK,MAAM;QACrB,UAAU,KAAK,UAAU;QACzB,UAAU,KAAK,MAAM;QAErB,OAAO,UAAU,CAAC;IACpB,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE;KAC/B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,0BAA0B,CACxC,OAAiD;IAEjD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,MAAM,GAAG,GAAmD,EAAE,CAAC;IAC/D,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACxD,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,GAAG,CAAC,CAAC,YAAY;aAClB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAChC,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,CAAC,CAAC,OAAO;gBAClB,GAAG,CAAC,CAAC,YAAY;aAClB,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACpD,iEAAiE;YACjE,SAAS;QACX,CAAC;aAAM,CAAC;YACN,MAAM,UAAU,GAAG,CAAiB,CAAC,CAAC,sCAAsC;YAC5E,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,OAA4C;IAE5C,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,GAAG,GAAgC,EAAE,CAAC;IAC5C,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YAC5B,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC,CAAC;QAC9D,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACpC,IAAI,OAAO,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAChC,MAAM,IAAI,KAAK,CACb,kDAAkD,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CACtE,CAAC;YACJ,CAAC;YACD,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,YAAY,IAAI,EAAE,CAAC;YACpD,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE;oBACT,GAAG,EAAE,CAAC,CAAC,KAAK;oBACZ,GAAG,SAAS;iBACb;gBACD,GAAG,IAAI;aACR,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,KAAK,CACb,wDAAwD,IAAI,CAAC,SAAS,CACpE,CAAC,CACF,EAAE,CACJ,CAAC;QACJ,CAAC;aAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAC9B,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,EAAE,GAAG,CAAC,CAAC,YAAY,IAAI,EAAE,CAAC;YACtD,GAAG,CAAC,IAAI,CAAC;gBACP,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE;oBACX,IAAI,EAAE,CAAC,CAAC,KAAK;oBACb,GAAG,WAAW;iBACf;gBACD,GAAG,IAAI;aACR,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,MAAM,UAAU,GAAG,CAAiB,CAAC,CAAC,sCAAsC;YAC5E,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,aAAa,CAAC,IAAwB;IAC7C,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QACtE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,KAA4B;IAE5B,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAC5C,CAAC;IACD,MAAM,MAAM,GAAiC,EAAE,CAAC;IAChD,IAAI,mBAAmB,GAA+C,IAAI,CAAC;IAC3E,MAAM,qBAAqB,GAAG,GAAG,EAAE;QACjC,IAAI,mBAAmB,EAAE,CAAC;YACxB,IACE,CAAC,mBAAmB,CAAC,UAAU;gBAC/B,mBAAmB,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,EAC3C,CAAC;gBACD,OAAO,mBAAmB,CAAC,UAAU,CAAC;YACxC,CAAC;YACD,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YACjC,mBAAmB,GAAG,IAAI,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC;IACF,MAAM,sBAAsB,GAAG,GAAG,EAAE;QAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,mBAAmB,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC;QAC9D,CAAC;QACD,OAAO,mBAAmB,CAAC;IAC7B,CAAC,CAAC;IACF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;YAC7C,qBAAqB,EAAE,CAAC;YACxB,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAwC;oBACrD,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,0BAA0B,CAAC,OAAO,CAAC;oBAC5C,GAAG,YAAY;iBAChB,CAAC;gBAEF,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;gBACtD,IAAI,KAAK,EAAE,CAAC;oBACV,SAAS,CAAC,KAAK,GAAG;wBAChB,EAAE,EAAE,EAAE,EAAE,4EAA4E;wBACpF,GAAG,KAAK,CAAC,YAAY;qBACtB,CAAC;gBACJ,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,CAAC;iBAAM,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI;oBACJ,OAAO,EAAE,qBAAqB,CAAC,OAAO,CAAC;oBACvC,GAAG,YAAY;iBAChB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC;oBACV,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO;oBAChB,GAAG,YAAY;iBAChB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACrC,MAAM,IAAI,SAAS,CACjB,6DAA6D;gBAC3D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACvB,CAAC;QACJ,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;YAC5C,IAAI,IAAI,CAAC,IAAI,KAAK,kBAAkB,EAAE,CAAC;gBACrC,MAAM,IAAI,GAAG,sBAAsB,EAAE,CAAC;gBACtC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,IAAI,CAAC;gBACxB,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,EAAE,GACvC,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC;gBAChC,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,qBAAqB,EAAE,GACzD,YAAY,IAAI,EAAE,CAAC;gBAErB,SAAS,CAAC,IAAI,CAAC;oBACb,EAAE,EAAE,UAAU,CAAC,EAAE,IAAI,EAAE;oBACvB,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,kBAAkB;wBACxB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;4BACxB,OAAO,EAAE,UAAU,CAAC,YAAY,EAAE,OAAO,IAAI,EAAE;4BAC/C,MAAM,EAAE,UAAU,CAAC,MAAM;4BACzB,GAAG,YAAY;yBAChB,CAAC;wBACF,GAAG,qBAAqB;qBACzB;oBACD,GAAG,IAAI;iBACR,CAAC,CAAC;gBACH,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC5B,SAAS;YACX,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,SAAS,CACjB,sEAAsE;oBACpE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACvB,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IACL,IAAI,CAAC,IAAI,KAAK,eAAe;YAC7B,IAAI,CAAC,IAAI,KAAK,sBAAsB,EACpC,CAAC;YACD,MAAM,IAAI,SAAS,CACjB,uEAAuE;gBACrE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACvB,CAAC;QACJ,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACzC,MAAM,IAAI,GAAG,sBAAsB,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;YACxC,MAAM,QAAQ,GAAG,IAAI,CAAC;YACtB,SAAS,CAAC,IAAI,CAAC;gBACb,EAAE,EAAE,QAAQ,CAAC,MAAM;gBACnB,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ,CAAC,IAAI;oBACnB,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,IAAI;iBACtC;aACF,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC9B,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAsB,EAAE,CAAC;YAChD,qBAAqB,EAAE,CAAC;YACxB,MAAM,UAAU,GAAG,IAAI,CAAC;YACxB,IAAI,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CACjB,gEAAgE;oBAC9D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACvB,CAAC;YACJ,CAAC;YAED,MAAM,CAAC,IAAI,CAAC;gBACV,IAAI,EAAE,MAAM;gBACZ,YAAY,EAAE,UAAU,CAAC,MAAM;gBAC/B,OAAO,EAAE,UAAU,CAAC,MAAM,CAAC,IAAI;gBAC/B,GAAG,UAAU,CAAC,YAAY;aAC3B,CAAC,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC;gBACV,GAAG,IAAI,CAAC,YAAY;aACd,CAAC,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,MAAM,UAAU,GAAG,IAAoB,CAAC,CAAC,sCAAsC;YAC/E,MAAM,IAAI,KAAK,CAAC,sBAAsB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IACD,qBAAqB,EAAE,CAAC;IACxB,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAoB;IAC/C,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QAC7B,OAAO;YACL,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;gBACnC,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B;SACF,CAAC;IACJ,CAAC;IACD,MAAM,IAAI,KAAK,CACb,+EACE,IAAI,CAAC,IACP,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAClC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAChC,OAA0B;IAE1B,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,QAAQ,EAAE;YACR,IAAI,EAAE,OAAO,CAAC,QAAQ;YACtB,WAAW,EAAE,OAAO,CAAC,eAAe,IAAI,EAAE;YAC1C,UAAU,EAAE,OAAO,CAAC,eAAe;SACpC;KACF,CAAC;AACJ,CAAC"}