{"version": 3, "file": "openaiChatCompletionsModel.js", "sourceRoot": "", "sources": ["../src/openaiChatCompletionsModel.ts"], "names": [], "mappings": "OAAO,EAEL,KAAK,EACL,kBAAkB,EAClB,gBAAgB,EAChB,oBAAoB,EACpB,cAAc,GACf,MAAM,qBAAqB;OASrB,MAAM;OACN,EAAE,OAAO,EAAE;OASX,EAAE,uCAAuC,EAAE;OAC3C,EACL,iBAAiB,EACjB,YAAY,EACZ,kBAAkB,EAClB,eAAe,GAChB;AAGD,MAAM,CAAC,MAAM,OAAO,GAAG,SAAS,CAAC;AAEjC;;GAEG;AACH,MAAM,OAAO,0BAA0B;IACrC,OAAO,CAAS;IAChB,MAAM,CAAS;IACf,YAAY,MAAc,EAAE,KAAa;QACvC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAqB;QACrC,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;YACvD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC,aAAa;gBAChD,CAAC,CAAC;oBACE,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,WAAW;oBAC9C,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;oBACjC,iBAAiB,EAAE,OAAO,CAAC,aAAa,CAAC,gBAAgB;oBACzD,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,eAAe;iBACxD;gBACH,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;YACjE,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBACrC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAA+B,EAAE,CAAC;QAC9C,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;YAC5C,IAAI,OAAO,CAAC,OAAO,KAAK,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBAC9D,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,aAAa;4BACnB,IAAI,EAAE,OAAO,IAAI,EAAE;4BACnB,YAAY,EAAE,IAAI;yBACnB;qBACF;oBACD,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;gBACrC,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,SAAS;4BACf,OAAO,EAAE,OAAO,IAAI,EAAE;4BACtB,YAAY,EAAE,IAAI;yBACnB;qBACF;oBACD,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBACzB,MAAM,EAAE,IAAI,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;gBACtD,MAAM,CAAC,IAAI,CAAC;oBACV,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE;wBACP;4BACE,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,IAAI;4BACX,YAAY,EAAE,kBAAkB;yBACjC;qBACF;oBACD,MAAM,EAAE,WAAW;iBACpB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;gBAC9B,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;oBAC3C,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,qBAAqB,EAAE,GAAG,SAAS,CAAC;oBAC3D,MAAM,EACJ,SAAS,EAAE,IAAI,EACf,IAAI,EACJ,GAAG,qBAAqB,EACzB,GAAG,SAAS,CAAC,QAAQ,CAAC;oBACvB,MAAM,CAAC,IAAI,CAAC;wBACV,EAAE,EAAE,QAAQ,CAAC,EAAE;wBACf,IAAI,EAAE,eAAe;wBACrB,SAAS,EAAE,IAAI;wBACf,IAAI,EAAE,IAAI;wBACV,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,WAAW;wBACnB,YAAY,EAAE;4BACZ,GAAG,qBAAqB;4BACxB,GAAG,qBAAqB;yBACzB;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,aAAa,GAAkB;YACnC,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACnB,CAAC,CAAC,IAAI,KAAK,CAAC,eAAe,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAC5C,CAAC,CAAC,IAAI,KAAK,EAAE;YACf,MAAM;YACN,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,YAAY,EAAE,QAAQ;SACvB,CAAC;QAEF,OAAO,aAAa,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,CAAC,mBAAmB,CACxB,OAAqB;QAErB,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;QAClE,IAAI,CAAC;YACH,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,cAAc,CAAC,IAAI,CAAC,CAAC;YACvB,CAAC;YACD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAE9D,MAAM,QAAQ,GAA2C;gBACvD,EAAE,EAAE,OAAO;gBACX,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;gBACtC,KAAK,EAAE,IAAI,CAAC,MAAM;gBAClB,MAAM,EAAE,iBAAiB;gBACzB,OAAO,EAAE,EAAE;gBACX,KAAK,EAAE;oBACL,aAAa,EAAE,CAAC;oBAChB,iBAAiB,EAAE,CAAC;oBACpB,YAAY,EAAE,CAAC;iBAChB;aACF,CAAC;YACF,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,uCAAuC,CAC/D,QAAQ,EACR,MAAM,CACP,EAAE,CAAC;gBACF,MAAM,KAAK,CAAC;YACd,CAAC;YAED,IAAI,IAAI,IAAI,QAAQ,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;gBACjD,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpC,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,QAAQ,CAAC;oBACZ,OAAO,EAAE,0BAA0B;oBACnC,IAAI,EAAE;wBACJ,KAAK,EACH,OAAO,CAAC,OAAO,KAAK,IAAI;4BACtB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;4BACf,CAAC,CAAC,KAAK,YAAY,KAAK;gCACtB,CAAC,CAAC,KAAK,CAAC,IAAI;gCACZ,CAAC,CAAC,SAAS;qBAClB;iBACF,CAAC,CAAC;YACL,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,IAAI,EAAE,CAAC;gBACT,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,gBAAgB,EAAE,CAAC;YACrB,CAAC;QACH,CAAC;IACH,CAAC;IAeD,KAAK,CAAC,cAAc,CAClB,OAAqB,EACrB,IAA0C,EAC1C,MAAe;QAKf,MAAM,KAAK,GAAiD,EAAE,CAAC;QAC/D,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAClB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;gBACjC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;YACjC,CAAC;QACH,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACvC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QACD,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAE7D,IAAI,iBAAiB,GAAwB,SAAS,CAAC;QACvD,IAAI,OAAO,OAAO,CAAC,aAAa,CAAC,iBAAiB,KAAK,SAAS,EAAE,CAAC;YACjE,IAAI,OAAO,CAAC,aAAa,CAAC,iBAAiB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAClE,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;YACzE,CAAC;YAED,iBAAiB,GAAG,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC;QAC9D,CAAC;QAED,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,QAAQ,CAAC,OAAO,CAAC;gBACf,OAAO,EAAE,OAAO,CAAC,kBAAkB;gBACnC,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;YACrC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC;QACjC,CAAC;QAED,MAAM,WAAW,GAAG;YAClB,KAAK,EAAE,IAAI,CAAC,MAAM;YAClB,QAAQ;YACR,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;YACvC,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,WAAW;YAC9C,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,IAAI;YACjC,iBAAiB,EAAE,OAAO,CAAC,aAAa,CAAC,gBAAgB;YACzD,gBAAgB,EAAE,OAAO,CAAC,aAAa,CAAC,eAAe;YACvD,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,SAAS;YAC3C,WAAW,EAAE,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC;YAChE,eAAe,EAAE,cAAc;YAC/B,mBAAmB,EAAE,iBAAiB;YACtC,MAAM;YACN,KAAK,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK;YAClC,GAAG,OAAO,CAAC,aAAa,CAAC,YAAY;SACtC,CAAC;QAEF,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,CACV,8BAA8B,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CACrE,CAAC;QACJ,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,EAAE;YACzE,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;QAEH,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,CAAC,sBAAsB,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAC5E,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AAED,SAAS,iBAAiB,CACxB,UAAgC;IAEhC,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;QAC1B,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;IAC1B,CAAC;IAED,IAAI,UAAU,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;QACtC,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE;gBACX,IAAI,EAAE,UAAU,CAAC,IAAI;gBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,MAAM,EAAE,UAAU,CAAC,MAAM;aAC1B;SACF,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;AACjC,CAAC;AAED,SAAS,eAAe,CACtB,KAAsB;IAEtB,OAAO;QACL,QAAQ,EAAE,CAAC;QACX,YAAY,EAAE,KAAK,CAAC,aAAa;QACjC,aAAa,EAAE,KAAK,CAAC,iBAAiB;QACtC,YAAY,EAAE,KAAK,CAAC,YAAY;QAChC,oBAAoB,EAAE;YACpB,aAAa,EAAE,KAAK,CAAC,qBAAqB,EAAE,aAAa,IAAI,CAAC;SAC/D;QACD,qBAAqB,EAAE;YACrB,gBAAgB,EAAE,KAAK,CAAC,yBAAyB,EAAE,gBAAgB,IAAI,CAAC;SACzE;KACF,CAAC;AACJ,CAAC"}