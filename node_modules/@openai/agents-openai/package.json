{"name": "@openai/agents-openai", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.0.9", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.ts", "default": "./dist/index.mjs"}}, "dependencies": {"debug": "^4.4.0", "openai": "^5.0.1", "@openai/zod": "npm:zod@^3.25.40", "@openai/agents-core": "0.0.9"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "devDependencies": {"@ai-sdk/provider": "^1.1.3", "@types/debug": "^4.1.12"}, "files": ["dist"], "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}}