import { Agent, AgentOutputType } from './agent';
import { Handoff } from './handoff';
import { ResolvedAgentOutput, HandoffsOutput, AgentInputItem, AgentOutputItem } from './types';
import { RunItem, RunToolApprovalItem } from './items';
import { ModelResponse } from './model';
import { Readable } from '@openai/agents-core/_shims';
import { ReadableStream } from './shims/interface';
import { RunStreamEvent } from './events';
import { RunState } from './runState';
import type { InputGuardrailResult, OutputGuardrailResult } from './guardrail';
/**
 * Data returned by the run() method of an agent.
 */
export interface RunResultData<TAgent extends Agent<any, any>, THandoffs extends (Agent<any, any> | Handoff<any>)[] = any[]> {
    /**
     * The original input items i.e. the items before run() was called. This may be mutated version
     * of the input, if there are handoff input filters that mutate the input.
     */
    input: string | AgentInputItem[];
    /**
     * The new items generated during the agent run. These include things like new messages, tool
     * calls and their outputs, etc.
     */
    newItems: RunItem[];
    /**
     * The raw LLM responses generated by the model during the agent run.
     */
    rawResponses: ModelResponse[];
    /**
     * The last response ID generated by the model during the agent run.
     */
    lastResponseId: string | undefined;
    /**
     * The last agent that was run
     */
    lastAgent: TAgent | undefined;
    /**
     * Guardrail results for the input messages.
     */
    inputGuardrailResults: InputGuardrailResult[];
    /**
     * Guardrail results for the final output of the agent.
     */
    outputGuardrailResults: OutputGuardrailResult[];
    /**
     * The output of the last agent, or any handoff agent.
     */
    finalOutput?: ResolvedAgentOutput<TAgent['outputType']> | HandoffsOutput<THandoffs>;
    /**
     * The interruptions that occurred during the agent run.
     */
    interruptions?: RunToolApprovalItem[];
    /**
     * The state of the run.
     */
    state: RunState<any, TAgent>;
}
declare class RunResultBase<TContext, TAgent extends Agent<TContext, any>> implements RunResultData<TAgent> {
    readonly state: RunState<TContext, TAgent>;
    constructor(state: RunState<TContext, TAgent>);
    /**
     * The history of the agent run. This includes the input items and the new items generated during
     * the agent run.
     *
     * This can be used as inputs for the next agent run.
     */
    get history(): AgentInputItem[];
    /**
     * The new items generated during the agent run. These include things like new messages, tool
     * calls and their outputs, etc.
     *
     * It does not include information about the agents and instead represents the model data.
     *
     * For the output including the agents, use the `newItems` property.
     */
    get output(): AgentOutputItem[];
    /**
     * A copy of the original input items.
     */
    get input(): string | AgentInputItem[];
    /**
     * The run items generated during the agent run. This associates the model data with the agents.
     *
     * For the model data that can be used as inputs for the next agent run, use the `output` property.
     */
    get newItems(): RunItem[];
    /**
     * The raw LLM responses generated by the model during the agent run.
     */
    get rawResponses(): ModelResponse[];
    /**
     * The last response ID generated by the model during the agent run.
     */
    get lastResponseId(): string | undefined;
    /**
     * The last agent that was run
     */
    get lastAgent(): TAgent | undefined;
    /**
     * Guardrail results for the input messages.
     */
    get inputGuardrailResults(): InputGuardrailResult[];
    /**
     * Guardrail results for the final output of the agent.
     */
    get outputGuardrailResults(): OutputGuardrailResult[];
    /**
     * Any interruptions that occurred during the agent run for example for tool approvals.
     */
    get interruptions(): RunToolApprovalItem[];
    /**
     * The final output of the agent. If the output type was set to anything other than `text`,
     * this will be parsed either as JSON or using the Zod schema you provided.
     */
    get finalOutput(): ResolvedAgentOutput<TAgent['outputType']> | undefined;
}
/**
 * The result of an agent run.
 */
export declare class RunResult<TContext, TAgent extends Agent<TContext, AgentOutputType>> extends RunResultBase<TContext, TAgent> {
    constructor(state: RunState<TContext, TAgent>);
}
/**
 * The result of an agent run in streaming mode.
 */
export declare class StreamedRunResult<TContext, TAgent extends Agent<TContext, AgentOutputType>> extends RunResultBase<TContext, TAgent> implements AsyncIterable<RunStreamEvent> {
    #private;
    /**
     * The current agent that is running
     */
    get currentAgent(): TAgent | undefined;
    /**
     * The current turn number
     */
    currentTurn: number;
    /**
     * The maximum number of turns that can be run
     */
    maxTurns: number | undefined;
    constructor(result?: {
        state: RunState<TContext, TAgent>;
        signal?: AbortSignal;
    });
    /**
     * Returns true if the stream has been cancelled.
     */
    get cancelled(): boolean;
    /**
     * Returns the underlying readable stream.
     * @returns A readable stream of the agent run.
     */
    toStream(): ReadableStream<RunStreamEvent>;
    /**
     * Await this promise to ensure that the stream has been completed if you are not consuming the
     * stream directly.
     */
    get completed(): Promise<void>;
    /**
     * Error thrown during the run, if any.
     */
    get error(): unknown;
    /**
     * Returns a readable stream of the final text output of the agent run.
     *
     * @param options - Options for the stream.
     * @param options.compatibleWithNodeStreams - Whether to use Node.js streams or web standard streams.
     * @returns A readable stream of the final output of the agent run.
     */
    toTextStream(): ReadableStream<string>;
    toTextStream(options?: {
        compatibleWithNodeStreams: true;
    }): Readable;
    toTextStream(options?: {
        compatibleWithNodeStreams?: false;
    }): ReadableStream<string>;
    [Symbol.asyncIterator](): AsyncIterator<RunStreamEvent>;
}
export {};
