{"version": 3, "file": "tool.js", "sourceRoot": "", "sources": ["../src/tool.ts"], "names": [], "mappings": "OAQO,EAAE,WAAW,EAAE;OACf,EAAE,kBAAkB,EAAE;OACtB,EAAE,+BAA+B,EAAE;OACnC,EAAE,WAAW,EAAE;OAEf,EAAE,kBAAkB,EAAE,SAAS,EAAE;OACjC,MAAM;OACN,EAAE,cAAc,EAAE;OAElB,EAAE,aAAa,EAAE;AAgFxB;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAC1B,OAAqE;IAErE,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,sBAAsB;QAC5C,QAAQ,EAAE,OAAO,CAAC,QAAQ;KAC3B,CAAC;AACJ,CAAC;AAgBD;;;;;;GAMG;AACH,MAAM,UAAU,aAAa,CAC3B,OAiBC;IAED,MAAM,YAAY,GAChB,OAAO,OAAO,CAAC,eAAe,KAAK,WAAW;QAC9C,OAAO,CAAC,eAAe,KAAK,OAAO;QACjC,CAAC,CAAC;YACE,IAAI,EAAE,KAAK;YACX,YAAY,EAAE,OAAO,CAAC,WAAW;YACjC,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,gBAAgB,EAAE,OAAO;YACzB,aAAa,EAAE,uBAAuB,CAAC,OAAO,CAAC,YAAY,CAAC;YAC5D,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB;QACH,CAAC,CAAC;YACE,IAAI,EAAE,KAAK;YACX,YAAY,EAAE,OAAO,CAAC,WAAW;YACjC,UAAU,EAAE,OAAO,CAAC,SAAS;YAC7B,aAAa,EAAE,uBAAuB,CAAC,OAAO,CAAC,YAAY,CAAC;YAC5D,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,gBAAgB,EACd,OAAO,OAAO,CAAC,eAAe,KAAK,QAAQ;gBACzC,CAAC,CAAC,QAAQ;gBACV,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC,eAAe,CAAC;YACnD,WAAW,EAAE,OAAO,CAAC,UAAU;SAChC,CAAC;IACR,OAAO;QACL,IAAI,EAAE,aAAa;QACnB,IAAI,EAAE,YAAY;QAClB,YAAY;KACb,CAAC;AACJ,CAAC;AA4KD;;;;;;;GAOG;AACH,SAAS,wBAAwB,CAAC,OAAmB,EAAE,KAAsB;IAC3E,MAAM,OAAO,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1E,OAAO,sEAAsE,OAAO,EAAE,CAAC;AACzF,CAAC;AAkHD;;;;;GAKG;AACH,MAAM,UAAU,IAAI,CAKlB,OAA0C;IAE1C,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI;QACvB,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC;QAClC,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,iBAAiB,GACrB,OAAO,OAAO,CAAC,aAAa,KAAK,WAAW;QAC1C,CAAC,CAAC,wBAAwB;QAC1B,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;IAE5B,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;IACJ,CAAC;IAED,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC;IAC1C,IAAI,CAAC,UAAU,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QACnD,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;IACpE,CAAC;IAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,+BAA+B,CACpE,OAAO,CAAC,UAAU,EAClB,IAAI,CACL,CAAC;IAEF,KAAK,UAAU,OAAO,CACpB,UAA+B,EAC/B,KAAa;QAEb,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,MAAM,WAAW,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAC/D,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YACnB,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;gBAC3B,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CAAC,+BAA+B,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;YAChE,CAAC;YACD,MAAM,IAAI,kBAAkB,CAAC,6BAA6B,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,CAAC,iBAAiB,IAAI,eAAe,KAAK,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QACzD,MAAM,YAAY,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QAE3C,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAC3B,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,YAAY,CAAC,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,CAAC,QAAQ,IAAI,cAAc,YAAY,EAAE,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,MAAgB,CAAC;IAC1B,CAAC;IAED,KAAK,UAAU,MAAM,CACnB,UAA+B,EAC/B,KAAa;QAEb,OAAO,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC,KAAK,CAAS,CAAC,KAAK,EAAE,EAAE;YACxD,IAAI,iBAAiB,EAAE,CAAC;gBACtB,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;gBACrC,WAAW,EAAE,QAAQ,CAAC;oBACpB,OAAO,EAAE,gCAAgC;oBACzC,IAAI,EAAE;wBACJ,SAAS,EAAE,IAAI;wBACf,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;qBACxB;iBACF,CAAC,CAAC;gBACH,OAAO,iBAAiB,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,aAAa,GACjB,OAAO,OAAO,CAAC,aAAa,KAAK,UAAU;QACzC,CAAC,CAAC,OAAO,CAAC,aAAa;QACvB,CAAC,CAAC,KAAK,IAAI,EAAE,CACT,OAAO,OAAO,CAAC,aAAa,KAAK,SAAS;YACxC,CAAC,CAAC,OAAO,CAAC,aAAa;YACvB,CAAC,CAAC,KAAK,CAAC;IAElB,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,IAAI;QACJ,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,UAAU;QACV,MAAM,EAAE,UAAU;QAClB,MAAM;QACN,aAAa;KACd,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,eAG7B;IACC,MAAM,MAAM,GAGR,EAAE,CAAC;IACP,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;QAC3B,MAAM,CAAC,MAAM,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;IACnE,CAAC;IACD,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;QAC1B,MAAM,CAAC,KAAK,GAAG,EAAE,UAAU,EAAE,eAAe,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;IACjE,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,uBAAuB,CAC9B,YAA6D;IAE7D,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;QACxC,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAChC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC;IACtC,CAAC;IACD,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,IAAI,EAAE,EAAE,CAAC;AACvD,CAAC"}