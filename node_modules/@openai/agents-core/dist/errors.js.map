{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../src/errors.ts"], "names": [], "mappings": "AASA;;GAEG;AACH,MAAM,OAAgB,WAAY,SAAQ,KAAK;IAC7C,KAAK,CAAkC;IAEvC,YAAY,OAAe,EAAE,KAAsC;QACjE,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;CACF;AAED;;;GAGG;AACH,MAAM,OAAO,WAAY,SAAQ,WAAW;CAAG;AAE/C;;GAEG;AACH,MAAM,OAAO,qBAAsB,SAAQ,WAAW;CAAG;AAEzD;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,WAAW;CAAG;AAEtD;;GAEG;AACH,MAAM,OAAO,SAAU,SAAQ,WAAW;CAAG;AAE7C;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,WAAW;IACtD,KAAK,CAAQ;IACb,YACE,OAAe,EACf,KAAY,EACZ,KAAsC;QAEtC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,aAAc,SAAQ,WAAW;IAC5C,KAAK,CAAQ;IACb,YACE,OAAe,EACf,KAAY,EACZ,KAAsC;QAEtC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,WAAW;IAC9D,MAAM,CAAuB;IAC7B,YACE,OAAe,EACf,MAA4B,EAC5B,KAA0B;QAE1B,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,gCAGX,SAAQ,WAAW;IACnB,MAAM,CAA4C;IAClD,YACE,OAAe,EACf,MAAiD,EACjD,KAA0B;QAE1B,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF"}