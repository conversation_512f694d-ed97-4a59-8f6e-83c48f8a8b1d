import { Agent } from './agent';
import * as protocol from './types/protocol';
export declare class RunItemBase {
    readonly type: string;
    rawItem?: protocol.ModelItem;
    toJSON(): {
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunMessageOutputItem extends RunItemBase {
    rawItem: protocol.AssistantMessageItem;
    agent: Agent;
    readonly type: "message_output_item";
    constructor(rawItem: protocol.AssistantMessageItem, agent: Agent);
    toJSON(): {
        agent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
    get content(): string;
}
export declare class RunToolCallItem extends RunItemBase {
    rawItem: protocol.ToolCallItem;
    agent: Agent;
    readonly type: "tool_call_item";
    constructor(rawItem: protocol.ToolCallItem, agent: Agent);
    toJSON(): {
        agent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunToolCallOutputItem extends RunItemBase {
    rawItem: protocol.FunctionCallResultItem | protocol.ComputerCallResultItem;
    agent: Agent<any, any>;
    output: string | unknown;
    readonly type: "tool_call_output_item";
    constructor(rawItem: protocol.FunctionCallResultItem | protocol.ComputerCallResultItem, agent: Agent<any, any>, output: string | unknown);
    toJSON(): {
        agent: {
            name: string;
        };
        output: string;
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunReasoningItem extends RunItemBase {
    rawItem: protocol.ReasoningItem;
    agent: Agent;
    readonly type: "reasoning_item";
    constructor(rawItem: protocol.ReasoningItem, agent: Agent);
    toJSON(): {
        agent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunHandoffCallItem extends RunItemBase {
    rawItem: protocol.FunctionCallItem;
    agent: Agent;
    readonly type: "handoff_call_item";
    constructor(rawItem: protocol.FunctionCallItem, agent: Agent);
    toJSON(): {
        agent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunHandoffOutputItem extends RunItemBase {
    rawItem: protocol.FunctionCallResultItem;
    sourceAgent: Agent<any, any>;
    targetAgent: Agent<any, any>;
    readonly type: "handoff_output_item";
    constructor(rawItem: protocol.FunctionCallResultItem, sourceAgent: Agent<any, any>, targetAgent: Agent<any, any>);
    toJSON(): {
        sourceAgent: {
            name: string;
        };
        targetAgent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export declare class RunToolApprovalItem extends RunItemBase {
    rawItem: protocol.FunctionCallItem | protocol.HostedToolCallItem;
    agent: Agent<any, any>;
    readonly type: "tool_approval_item";
    constructor(rawItem: protocol.FunctionCallItem | protocol.HostedToolCallItem, agent: Agent<any, any>);
    toJSON(): {
        agent: {
            name: string;
        };
        type: string;
        rawItem: {
            status: "in_progress" | "completed" | "incomplete";
            role: "assistant";
            content: ({
                type: "refusal";
                refusal: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "output_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            } | {
                type: "image";
                image: string;
                providerData?: Record<string, any> | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "user";
            content: string | ({
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_image";
                image: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "input_file";
                file: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
            } | {
                type: "audio";
                audio: string | {
                    id: string;
                };
                providerData?: Record<string, any> | undefined;
                format?: string | null | undefined;
                transcript?: string | null | undefined;
            })[];
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            role: "system";
            content: string;
            type?: "message" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "hosted_tool_call";
            name: string;
            status?: string | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
            arguments?: string | undefined;
            output?: string | undefined;
        } | {
            type: "function_call";
            name: string;
            arguments: string;
            callId: string;
            status?: "in_progress" | "completed" | "incomplete" | undefined;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "function_call_result";
            status: "in_progress" | "completed" | "incomplete";
            name: string;
            output: {
                type: "text";
                text: string;
                providerData?: Record<string, any> | undefined;
            } | {
                type: "image";
                data: string;
                mediaType: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call";
            status: "in_progress" | "completed" | "incomplete";
            callId: string;
            action: {
                type: "screenshot";
            } | {
                type: "click";
                x: number;
                y: number;
                button: "left" | "right" | "wheel" | "back" | "forward";
            } | {
                type: "double_click";
                x: number;
                y: number;
            } | {
                type: "scroll";
                x: number;
                y: number;
                scroll_x: number;
                scroll_y: number;
            } | {
                type: "type";
                text: string;
            } | {
                type: "wait";
            } | {
                type: "move";
                x: number;
                y: number;
            } | {
                keys: string[];
                type: "keypress";
            } | {
                path: {
                    x: number;
                    y: number;
                }[];
                type: "drag";
            };
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "computer_call_result";
            output: {
                type: "computer_screenshot";
                data: string;
                providerData?: Record<string, any> | undefined;
            };
            callId: string;
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "reasoning";
            content: {
                type: "input_text";
                text: string;
                providerData?: Record<string, any> | undefined;
            }[];
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | {
            type: "unknown";
            providerData?: Record<string, any> | undefined;
            id?: string | undefined;
        } | undefined;
    };
}
export type RunItem = RunMessageOutputItem | RunToolCallItem | RunReasoningItem | RunHandoffCallItem | RunToolCallOutputItem | RunHandoffOutputItem | RunToolApprovalItem;
/**
 * Extract all text output from a list of run items by concatenating the content of all
 * message output items.
 *
 * @param items - The list of run items to extract text from.
 * @returns A string of all the text output from the run items.
 */
export declare function extractAllTextOutput(items: RunItem[]): string;
