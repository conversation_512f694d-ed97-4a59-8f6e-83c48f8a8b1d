{"version": 3, "file": "protocol.mjs", "sourceRoot": "", "sources": ["../../src/types/protocol.ts"], "names": [], "mappings": "OAAO,EAAE,CAAC,EAAE,MAAM,gBAAgB;AAElC,+BAA+B;AAC/B,oBAAoB;AACpB,+BAA+B;AAE/B;;;GAGG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC;IACjC;;;OAGG;IACH,YAAY,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE;CACvD,CAAC,CAAC;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAM,CAAC;IACxC;;;OAGG;IACH,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC1B,CAAC,CAAC;AAIH,+BAA+B;AAC/B,gBAAgB;AAChB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;IACvC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;IAC1B;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;CACpB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;IAC1C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;IAC9B;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;CACjB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;IACzC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IAC7B;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;CACjB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;IAC1C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;IAE9B;;OAEG;IACH,KAAK,EAAE,CAAC;SACL,MAAM,EAAE;SACR,EAAE,CACD,CAAC,CAAC,MAAM,CAAC;QACP,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;KACf,CAAC,CACH;SACA,QAAQ,CAAC,qDAAqD,CAAC;CACnE,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;IACzC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IAE7B;;OAEG;IACH,IAAI,EAAE,CAAC;SACJ,MAAM,EAAE;SACR,EAAE,CACD,CAAC,CAAC,MAAM,CAAC;QACP,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;KACf,CAAC,CACH;SACA,QAAQ,CAAC,mDAAmD,CAAC;CACjE,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;IAC5C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAExB;;OAEG;IACH,KAAK,EAAE,CAAC;SACL,MAAM,EAAE;SACR,EAAE,CACD,CAAC,CAAC,MAAM,CAAC;QACP,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;KACf,CAAC,CACH;SACA,QAAQ,CAAC,sCAAsC,CAAC;IAEnD;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;IAExC;;OAEG;IACH,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE;CAC7C,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC;IAC5C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAExB;;OAEG;IACH,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;CACxD,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC;IAC9C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;CACjB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC;IAC/C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAExB;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;IAEtD;;OAEG;IACH,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;CAC/D,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,kBAAkB,GAAG,UAAU,CAAC,MAAM,CAAC;IAClD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,qBAAqB,CAAC;IAEtC;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;CAC9D,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IAC1D,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QACxB,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;KAC9D,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;QAC/B,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;KACd,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;QACzB,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;QACpB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;KACrB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;QACvB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;IACrC,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;QACvB,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;QACb,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE;KACd,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;QAC3B,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KAC1B,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;QACvB,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;KAC1D,CAAC;CACH,CAAC,CAAC;AAIH,+BAA+B;AAC/B,gBAAgB;AAChB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IAC3D,UAAU;IACV,OAAO;IACP,SAAS;IACT,YAAY;IACZ,YAAY;CACb,CAAC,CAAC;AAIH,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;IAClC;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE;CACtC,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,oBAAoB,GAAG,WAAW,CAAC,MAAM,CAAC;IACrD;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;IAE5B;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAE1D;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC;CACnC,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IACtD,SAAS;IACT,UAAU;IACV,SAAS;IACT,YAAY;CACb,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC;IAChD,8BAA8B;IAE9B;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAEvB;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;CAC7C,CAAC,CAAC;AAIH,MAAM,iBAAiB,GAAG,WAAW,CAAC,MAAM,CAAC;IAC3C,8BAA8B;IAE9B;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAEzB;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;CACpB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IACtD,iBAAiB;IACjB,oBAAoB;IACpB,eAAe;CAChB,CAAC,CAAC;AAIH,+BAA+B;AAC/B,kBAAkB;AAClB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,kBAAkB,GAAG,QAAQ,CAAC,MAAM,CAAC;IAChD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;IACnC;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IAExD;;OAEG;IACH,SAAS,EAAE,CAAC;SACT,MAAM,EAAE;SACR,QAAQ,CAAC,uCAAuC,CAAC;SACjD,QAAQ,EAAE;IAEb;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAE7B;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CAC9B,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC;IAC9C,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;IAChC;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;IAEtD;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IAErD;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,EAAE;IAErE;;OAEG;IACH,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;CACtB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,MAAM,CAAC;IACpD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC;IACvC;;OAEG;IACH,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;IAEjD;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;IAEtD;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAE1D;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;CACxE,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,mBAAmB,GAAG,QAAQ,CAAC,MAAM,CAAC;IACjD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;IAEhC;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IAE1D;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAE1D;;OAEG;IACH,MAAM,EAAE,eAAe;CACxB,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,sBAAsB,GAAG,QAAQ,CAAC,MAAM,CAAC;IACpD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC;IAEvC;;OAEG;IACH,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IAE1D;;OAEG;IACH,MAAM,EAAE,kBAAkB;CAC3B,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IACvD,mBAAmB;IACnB,gBAAgB;IAChB,kBAAkB;CACnB,CAAC,CAAC;AAIH,+BAA+B;AAC/B,qBAAqB;AACrB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;IAC7C,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACzB,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC;IAE5B;;OAEG;IACH,OAAO,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;CAC5B,CAAC,CAAC;AAIH;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,CAAC;IACzC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;CAC3B,CAAC,CAAC;AAIH,+BAA+B;AAC/B,oBAAoB;AACpB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IAC1D,oBAAoB;IACpB,kBAAkB;IAClB,gBAAgB;IAChB,mBAAmB;IACnB,aAAa;IACb,WAAW;CACZ,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;IAC/B,eAAe;IACf,oBAAoB;IACpB,iBAAiB;IACjB,kBAAkB;IAClB,gBAAgB;IAChB,mBAAmB;IACnB,sBAAsB;IACtB,sBAAsB;IACtB,aAAa;IACb,WAAW;CACZ,CAAC,CAAC;AAIH,+BAA+B;AAC/B,kBAAkB;AAClB,+BAA+B;AAE/B,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC;IAChC,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC/B,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;IACvB,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE;IACxB,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;IACvB,kBAAkB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IAC/D,mBAAmB,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;CACjE,CAAC,CAAC;AAIH,+BAA+B;AAC/B,qBAAqB;AACrB,+BAA+B;AAE/B;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,UAAU,CAAC,MAAM,CAAC;IACrD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC;IACpC;;OAEG;IACH,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;CAClB,CAAC,CAAC;AAIH;;GAEG;AACH,MAAM,CAAC,MAAM,0BAA0B,GAAG,UAAU,CAAC,MAAM,CAAC;IAC1D,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,kBAAkB,CAAC;CACpC,CAAC,CAAC;AAMH;;GAEG;AACH,MAAM,CAAC,MAAM,4BAA4B,GAAG,UAAU,CAAC,MAAM,CAAC;IAC5D,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;IAChC;;OAEG;IACH,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC;QAC1B;;WAEG;QACH,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;QAEd;;WAEG;QACH,KAAK,EAAE,SAAS;QAEhB;;WAEG;QACH,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC;KACjC,CAAC;CACH,CAAC,CAAC;AAMH;;;GAGG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,UAAU,CAAC,MAAM,CAAC;IACtD,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IACxB,KAAK,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;CACpD,CAAC,CAAC;AAGH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,MAAM,EAAE;IACtD,qBAAqB;IACrB,4BAA4B;IAC5B,0BAA0B;IAC1B,sBAAsB;CACvB,CAAC,CAAC"}