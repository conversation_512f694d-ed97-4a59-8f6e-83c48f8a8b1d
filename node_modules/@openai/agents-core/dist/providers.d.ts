import { ModelProvider } from './model';
/**
 * Set the model provider used when no explicit provider is supplied.
 *
 * @param provider - The provider to use by default.
 */
export declare function setDefaultModelProvider(provider: ModelProvider): void;
/**
 * Returns the default model provider.
 *
 * @returns The default model provider.
 */
export declare function getDefaultModelProvider(): ModelProvider;
