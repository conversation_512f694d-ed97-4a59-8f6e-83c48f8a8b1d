/**
 * Loads environment variables from the process environment.
 *
 * @returns An object containing the environment variables.
 */
export declare function loadEnv(): Record<string, string | undefined>;
/**
 * Global configuration for tracing.
 */
export declare const tracing: {
    readonly disabled: boolean;
};
/**
 * Global configuration for logging.
 */
export declare const logging: {
    readonly dontLogModelData: boolean;
    readonly dontLogToolData: boolean;
};
