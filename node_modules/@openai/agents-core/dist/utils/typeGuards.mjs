/**
 * Verifies that an input is a ZodObject without needing to have <PERSON><PERSON> at runtime since it's an
 * optional dependency.
 * @param input
 * @returns
 */
export function isZodObject(input) {
    return (typeof input === 'object' &&
        input !== null &&
        '_def' in input &&
        typeof input._def === 'object' &&
        input._def !== null &&
        'typeName' in input._def &&
        input._def.typeName === 'ZodObject');
}
/**
 * Verifies that an input is an object with an `input` property.
 * @param input
 * @returns
 */
export function isAgentToolInput(input) {
    return (typeof input === 'object' &&
        input !== null &&
        'input' in input &&
        typeof input.input === 'string');
}
//# sourceMappingURL=typeGuards.mjs.map