import { RunHandoffCallItem, RunHandoffOutputItem, RunToolCallItem, RunToolCallOutputItem, } from "../items.js";
const TOOL_TYPES = new Set([
    'function_call',
    'function_call_result',
    'computer_call',
    'computer_call_result',
    'hosted_tool_call',
]);
/**
 * Filters out all tool items: file search, web serach and function calls+output
 * @param handoffInputData
 * @returns
 */
export function removeAllTools(handoffInputData) {
    const { inputHistory, preHandoffItems, newItems } = handoffInputData;
    const filteredHistory = Array.isArray(inputHistory)
        ? removeToolTypesFromInput(inputHistory)
        : inputHistory;
    const filteredPreHandoffItems = removeToolsFromItems(preHandoffItems);
    const filteredNewItems = removeToolsFromItems(newItems);
    return {
        inputHistory: filteredHistory,
        preHandoffItems: filteredPreHandoffItems,
        newItems: filteredNewItems,
    };
}
function removeToolsFromItems(items) {
    return items.filter((item) => !(item instanceof RunHandoffCallItem) &&
        !(item instanceof RunHandoffOutputItem) &&
        !(item instanceof RunToolCallItem) &&
        !(item instanceof RunToolCallOutputItem));
}
function removeToolTypesFromInput(items) {
    return items.filter((item) => !TOOL_TYPES.has(item.type ?? ''));
}
//# sourceMappingURL=handoffFilters.js.map