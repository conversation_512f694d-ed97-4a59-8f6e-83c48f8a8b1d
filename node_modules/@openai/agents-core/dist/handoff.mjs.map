{"version": 3, "file": "handoff.mjs", "sourceRoot": "", "sources": ["../src/handoff.ts"], "names": [], "mappings": "OAUO,EAAE,kBAAkB,EAAE,SAAS,EAAE;OAEjC,EAAE,kBAAkB,EAAE;OACtB,EAAE,+BAA+B,EAAE;OACnC,EAAE,qBAAqB,EAAE;OACzB,MAAM;AAyBb;;;;;GAKG;AACH,MAAM,UAAU,kBAAkB,CAChC,KAA+B;IAE/B,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AACnD,CAAC;AAED;;;;;GAKG;AACH,SAAS,sBAAsB,CAC7B,KAA+B;IAE/B,OAAO,eAAe,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;AACzD,CAAC;AAED;;;;;GAKG;AACH,SAAS,6BAA6B,CAGpC,KAA+B;IAC/B,OAAO,kBAAkB,KAAK,CAAC,IAAI,iCACjC,KAAK,CAAC,kBAAkB,IAAI,EAC9B,EAAE,CAAC;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,OAAO,OAAO;IAIlB;;OAEG;IACI,QAAQ,CAAS;IAExB;;OAEG;IACI,eAAe,CAAS;IAE/B;;OAEG;IACI,eAAe,GAA0B;QAC9C,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;QACZ,oBAAoB,EAAE,KAAK;KAC5B,CAAC;IAEF;;;OAGG;IACI,gBAAgB,GAAY,IAAI,CAAC;IAExC;;;;;;OAMG;IACI,eAAe,CAG4C;IAElE;;OAEG;IACI,SAAS,CAAS;IAEzB;;;;;;;;;;OAUG;IACI,WAAW,CAAsB;IAExC;;OAEG;IACI,KAAK,CAA2B;IAEvC;;OAEG;IACH,wBAAwB;QACtB,OAAO;YACL,IAAI,EAAE,UAAmB;YACzB,IAAI,EAAE,IAAI,CAAC,QAAQ;YACnB,WAAW,EAAE,IAAI,CAAC,eAAe;YACjC,UAAU,EAAE,IAAI,CAAC,eAAe;YAChC,MAAM,EAAE,IAAI,CAAC,gBAAgB;SAC9B,CAAC;IACJ,CAAC;IAED,YACE,KAA+B,EAC/B,eAGiE;QAEjE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC;QAC5B,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,QAAQ,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,6BAA6B,CAAC,KAAK,CAAC,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;CACF;AAyCD;;;;;;;;GAQG;AACH,MAAM,UAAU,OAAO,CAIrB,KAA+B,EAAE,SAAoC,EAAE;IACvE,IAAI,MAAM,GAAkD,SAAS,CAAC;IAEtE,MAAM,YAAY,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;IACxC,MAAM,YAAY,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;IACxC,MAAM,mCAAmC,GAAG,YAAY,KAAK,YAAY,CAAC;IAE1E,IAAI,CAAC,mCAAmC,EAAE,CAAC;QACzC,MAAM,IAAI,SAAS,CACjB,sEAAsE,CACvE,CAAC;IACJ,CAAC;IAED,KAAK,UAAU,eAAe,CAC5B,OAAwB,EACxB,eAAwB;QAExB,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,qBAAqB,CAAC;oBACpB,OAAO,EAAE,sDAAsD,eAAe,EAAE;oBAChF,IAAI,EAAE;wBACJ,OAAO,EAAE,gBAAgB;qBAC1B;iBACF,CAAC,CAAC;gBACH,MAAM,IAAI,kBAAkB,CAC1B,2CAA2C,CAC5C,CAAC;YACJ,CAAC;YACD,IAAI,CAAC;gBACH,kEAAkE;gBAClE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,CAAC;gBAC7C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACrB,MAAM,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,qBAAqB,CAAC;oBACpB,OAAO,EAAE,uBAAuB;oBAChC,IAAI,EAAE,EAAE;iBACT,CAAC,CAAC;gBACH,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC;oBAC5B,MAAM,CAAC,KAAK,CACV,8BAA8B,eAAe,YAAY,KAAK,EAAE,CACjE,CAAC;gBACJ,CAAC;gBACD,MAAM,IAAI,kBAAkB,CAAC,uBAAuB,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;IAEpD,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;QACrB,MAAM,MAAM,GAAG,+BAA+B,CAC5C,MAAM,CAAC,SAAS,EAChB,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC;QACxC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAChC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;IACzB,CAAC;IAED,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC5B,OAAO,CAAC,QAAQ,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC7C,CAAC;IAED,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC;QACnC,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,uBAAuB,CAAC;IAC3D,CAAC;IAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;QACvB,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;IAC3C,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,UAAU,CACxB,KAA4D;IAE5D,IAAI,KAAK,YAAY,OAAO,EAAE,CAAC;QAC7B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;AACxB,CAAC"}