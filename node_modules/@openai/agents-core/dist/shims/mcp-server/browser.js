import { BaseMCPServerStdio, BaseMCPServerStreamableHttp, } from "../../mcp.js";
export class MCPServerStdio extends BaseMCPServerStdio {
    constructor(params) {
        super(params);
    }
    get name() {
        return 'MCPServerStdio';
    }
    connect() {
        throw new Error('Method not implemented.');
    }
    close() {
        throw new Error('Method not implemented.');
    }
    listTools() {
        throw new Error('Method not implemented.');
    }
    callTool(_toolName, _args) {
        throw new Error('Method not implemented.');
    }
}
export class MCPServerStreamableHttp extends BaseMCPServerStreamableHttp {
    constructor(params) {
        super(params);
    }
    get name() {
        return 'MCPServerStdio';
    }
    connect() {
        throw new Error('Method not implemented.');
    }
    close() {
        throw new Error('Method not implemented.');
    }
    listTools() {
        throw new Error('Method not implemented.');
    }
    callTool(_toolName, _args) {
        throw new Error('Method not implemented.');
    }
}
//# sourceMappingURL=browser.js.map