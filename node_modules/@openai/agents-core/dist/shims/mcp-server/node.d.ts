import type { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { BaseMCPServerStdio, BaseMCPServerStreamableHttp, CallToolResultContent, DefaultMCPServerStdioOptions, InitializeResult, MCPServerStdioOptions, MCPServerStreamableHttpOptions, MCPTool } from '../../mcp';
export interface SessionMessage {
    message: any;
}
export declare class NodeMCPServerStdio extends BaseMCPServerStdio {
    protected session: Client | null;
    protected _cacheDirty: boolean;
    protected _toolsList: any[];
    protected serverInitializeResult: InitializeResult | null;
    protected clientSessionTimeoutSeconds?: number;
    params: DefaultMCPServerStdioOptions;
    private _name;
    private transport;
    constructor(params: MCPServerStdioOptions);
    connect(): Promise<void>;
    invalidateToolsCache(): void;
    listTools(): Promise<MCPTool[]>;
    callTool(toolName: string, args: Record<string, unknown> | null): Promise<CallToolResultContent>;
    get name(): string;
    close(): Promise<void>;
}
export declare class NodeMCPServerStreamableHttp extends BaseMCPServerStreamableHttp {
    protected session: Client | null;
    protected _cacheDirty: boolean;
    protected _toolsList: any[];
    protected serverInitializeResult: InitializeResult | null;
    protected clientSessionTimeoutSeconds?: number;
    params: MCPServerStreamableHttpOptions;
    private _name;
    private transport;
    constructor(params: MCPServerStreamableHttpOptions);
    connect(): Promise<void>;
    invalidateToolsCache(): void;
    listTools(): Promise<MCPTool[]>;
    callTool(toolName: string, args: Record<string, unknown> | null): Promise<CallToolResultContent>;
    get name(): string;
    close(): Promise<void>;
}
