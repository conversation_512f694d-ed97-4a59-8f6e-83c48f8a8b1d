{"version": 3, "file": "node.js", "sourceRoot": "", "sources": ["../../../src/shims/mcp-server/node.ts"], "names": [], "mappings": "OAEO,EACL,kBAAkB,EAClB,2BAA2B,EAO3B,0BAA0B,GAC3B;OACM,MAAM;AAMb,SAAS,cAAc,CAAC,KAAc;IACpC,MAAM,CAAC,KAAK,CACV;;;;KAIC,CAAC,IAAI,EAAE,CACT,CAAC;IACF,MAAM,KAAK,CAAC;AACd,CAAC;AAED,MAAM,OAAO,kBAAmB,SAAQ,kBAAkB;IAC9C,OAAO,GAAkB,IAAI,CAAC;IAC9B,WAAW,GAAG,IAAI,CAAC;IACnB,UAAU,GAAU,EAAE,CAAC;IACvB,sBAAsB,GAA4B,IAAI,CAAC;IACvD,2BAA2B,CAAU;IAE/C,MAAM,CAA+B;IAC7B,KAAK,CAAS;IACd,SAAS,GAAQ,IAAI,CAAC;IAE9B,YAAY,MAA6B;QACvC,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,2BAA2B,IAAI,CAAC,CAAC;QAC3E,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC/C,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,uBAAuB,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;YAChE,CAAC;YACD,IAAI,CAAC,MAAM,GAAG;gBACZ,GAAG,MAAM;gBACT,OAAO,EAAE,OAAO;gBAChB,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,OAAO;gBACpC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,QAAQ;aAC9D,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAC3C,2CAA2C,EAC3C,KAAK,CAAC,cAAc,CAAC,CAAC;YACxB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAC7B,2CAA2C,EAC3C,KAAK,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,oBAAoB,CAAC;gBACxC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO;gBAC5B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI;gBACtB,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG;gBACpB,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG;aACrB,CAAC,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC;gBACxB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,OAAO,EAAE,OAAO,EAAE,yCAAyC;aAC5D,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,CAAC,sBAAsB,GAAG;gBAC5B,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;aAC/B,CAAC;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,CAAC,CAAC;QACV,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,4BAA4B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,oBAAoB;QAClB,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,yGAAyG;IACzG,KAAK,CAAC,SAAS;QACb,MAAM,EAAE,qBAAqB,EAAE,GAAG,MAAM,MAAM,CAC5C,oCAAoC,EACpC,KAAK,CAAC,cAAc,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;QAC9D,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,QAAgB,EAChB,IAAoC;QAEpC,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAC3C,oCAAoC,EACpC,KAAK,CAAC,cAAc,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC3C,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,CACX,GAAG,EAAE,CACH,eAAe,QAAQ,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAC/F,CAAC;QACF,OAAO,MAA+B,CAAC;IACzC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,2BAA4B,SAAQ,2BAA2B;IAChE,OAAO,GAAkB,IAAI,CAAC;IAC9B,WAAW,GAAG,IAAI,CAAC;IACnB,UAAU,GAAU,EAAE,CAAC;IACvB,sBAAsB,GAA4B,IAAI,CAAC;IACvD,2BAA2B,CAAU;IAE/C,MAAM,CAAiC;IAC/B,KAAK,CAAS;IACd,SAAS,GAAQ,IAAI,CAAC;IAE9B,YAAY,MAAsC;QAChD,KAAK,CAAC,MAAM,CAAC,CAAC;QACd,IAAI,CAAC,2BAA2B,GAAG,MAAM,CAAC,2BAA2B,IAAI,CAAC,CAAC;QAC3E,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,oBAAoB,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,MAAM,EAAE,6BAA6B,EAAE,GAAG,MAAM,MAAM,CACpD,oDAAoD,EACpD,KAAK,CAAC,cAAc,CAAC,CAAC;YACxB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,MAAM,CAC7B,2CAA2C,EAC3C,KAAK,CAAC,cAAc,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,6BAA6B,CAChD,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EACxB;gBACE,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY;gBACtC,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,mBAAmB,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB;gBACpD,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS;aACjC,CACF,CAAC;YACF,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,CAAC;gBACxB,IAAI,EAAE,IAAI,CAAC,KAAK;gBAChB,OAAO,EAAE,OAAO,EAAE,yCAAyC;aAC5D,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC3C,IAAI,CAAC,sBAAsB,GAAG;gBAC5B,UAAU,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;aAC/B,CAAC;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,CAAC;YACvD,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,CAAC,CAAC;QACV,CAAC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,4BAA4B,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,oBAAoB;QAClB,0BAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,yGAAyG;IACzG,KAAK,CAAC,SAAS;QACb,MAAM,EAAE,qBAAqB,EAAE,GAAG,MAAM,MAAM,CAC5C,oCAAoC,EACpC,KAAK,CAAC,cAAc,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAChE,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;QAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,iBAAiB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC;QAC9D,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,QAAgB,EAChB,IAAoC;QAEpC,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM,MAAM,CAC3C,oCAAoC,EACpC,KAAK,CAAC,cAAc,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACb,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QACD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC3C,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,oBAAoB,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,QAAQ,CACX,GAAG,EAAE,CACH,eAAe,QAAQ,WAAW,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAC/F,CAAC;QACF,OAAO,MAA+B,CAAC;IACzC,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACxB,CAAC;QACD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;CACF"}