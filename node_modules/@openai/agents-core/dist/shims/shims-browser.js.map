{"version": 3, "file": "shims-browser.js", "sourceRoot": "", "sources": ["../../src/shims/shims-browser.ts"], "names": [], "mappings": "AAAA,2BAA2B;AAK3B,yDAAyD;AACzD,gGAAgG;AAChG,MAAM,UAAU,OAAO;IACrB,OAAO,EAAE,CAAC;AACZ,CAAC;AAID,MAAM,OAAO,mBAAmB;IAI9B,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAE5B,EAAE,CACA,IAAO,EACP,QAA0C;QAE1C,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAC3B,IAAc,EACd,CAAC,CAAC,KAAkB,EAAE,EAAE,CACtB,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAkB,CACtD,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG,CACD,IAAO,EACP,QAA0C;QAE1C,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAC9B,IAAc,EACd,CAAC,CAAC,KAAkB,EAAE,EAAE,CACtB,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,CAAkB,CACtD,CAAC;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAA6B,IAAO,EAAE,GAAG,IAAmB;QAC9D,MAAM,KAAK,GAAG,IAAI,WAAW,CAAC,IAAc,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;QAChE,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;IAED,IAAI,CACF,IAAO,EACP,QAA0C;QAE1C,MAAM,OAAO,GAAG,CAAC,GAAG,IAAmB,EAAE,EAAE;YACzC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACxB,QAAQ,CAAC,GAAG,IAAI,CAAC,CAAC;QACpB,CAAC,CAAC;QACF,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,OAAO,EAAE,mBAAmB,IAAI,mBAAmB,EAAE,CAAC;AAEtD,MAAM,CAAC,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzD,MAAM,CAAC,MAAM,QAAQ,GAAG,MAAM,QAAQ;IACpC,gBAAe,CAAC;IAChB,MAAM,CACJ,YAA4B,EAC5B,QAIC,IACA,CAAC;IACJ,WAAW,CACT,UAA2B,EAC3B,QAIC,IACA,CAAC;CACL,CAAC;AACF,MAAM,CAAC,MAAM,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;AACxD,MAAM,CAAC,MAAM,wBAAwB,GACnC,UAAU,CAAC,+BAA+B,CAAC;AAC7C,MAAM,CAAC,MAAM,eAAe,GAAG,UAAU,CAAC,eAAe,CAAC;AAE1D,MAAM,OAAO,iBAAiB;IAC5B,OAAO,GAAG,IAAI,CAAC;IACf,gBAAe,CAAC;IAChB,GAAG,CAAC,OAAY,EAAE,EAAa;QAC7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,OAAO,EAAE,EAAE,CAAC;IACd,CAAC;IACD,QAAQ;QACN,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IACD,SAAS,CAAC,OAAY;QACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AAED,MAAM,UAAU,oBAAoB;IAClC,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,6BAA6B;IAC3C,OAAO,KAAK,CAAC;AACf,CAAC;OAEM,EAAE,cAAc,EAAE,uBAAuB,EAAE;AAElD,MAAM,YAAY;IAChB,gBAAe,CAAC;IAChB,UAAU,CAAC,QAAoB,EAAE,EAAU;QACzC,MAAM,OAAO,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG;YACT,OAAO,OAAO,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;QAClE,OAAO,CAAC,KAAK;YACX,OAAO,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;QACtE,OAAO,CAAC,MAAM;YACZ,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;QACrE,OAAO,CAAC,OAAO;YACb,OAAO,OAAO,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,YAAY,CAAC,SAAgD;QAC3D,MAAM,CAAC,YAAY,CAAC,SAAmB,CAAC,CAAC;IAC3C,CAAC;CACF;AACD,MAAM,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,CAAC"}