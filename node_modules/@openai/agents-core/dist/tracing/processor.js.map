{"version": 3, "file": "processor.js", "sourceRoot": "", "sources": ["../../src/tracing/processor.ts"], "names": [], "mappings": "OAEO,MAAM;OACN,EACL,KAAK,IAAI,MAAM,EACf,6BAA6B,GAC9B,MAAM,4BAA4B;OAE5B,EAAE,OAAO,EAAE;AAyDlB;;GAEG;AACH,MAAM,OAAO,mBAAmB;IAC9B,KAAK,CAAC,MAAM,CAAC,KAAuB;QAClC,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACrD,OAAO;QACT,CAAC;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC1B,OAAO,CAAC,GAAG,CACT,mCAAmC,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC,IAAI,EAAE,CACpE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAqBD,MAAM,OAAO,mBAAmB;IACrB,aAAa,CAAS;IACtB,aAAa,CAAS;IACtB,cAAc,CAAS;IACvB,kBAAkB,CAAS;IAC3B,SAAS,CAAkB;IAEpC,OAAO,GAAwB,EAAE,CAAC;IAClC,MAAM,CAAQ;IACd,QAAQ,GAAmB,IAAI,CAAC;IAChC,iBAAiB,GAAG,KAAK,CAAC;IAC1B,uBAAuB,GAA2B,IAAI,CAAC;IAEvD,YACE,QAAyB,EACzB,EACE,YAAY,GAAG,IAAI,EACnB,YAAY,GAAG,GAAG,EAClB,aAAa,GAAG,IAAI,EAAE,YAAY;IAClC,kBAAkB,GAAG,GAAG,MACM,EAAE;QAElC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,kBAAkB,GAAG,YAAY,GAAG,kBAAkB,CAAC;QAC5D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,6BAA6B,EAAE,EAAE,CAAC;YACpC,IAAI,CAAC,KAAK,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,KAAK,CACV,uJAAuJ,CACxJ,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,uBAAuB,GAAG,IAAI,eAAe,EAAE,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAkB;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACjD,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;YACtD,OAAO;QACT,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAClD,8BAA8B;YAC9B,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI,EAAE;YAChD,mBAAmB;YACnB,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;YAC5B,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QAExB,yFAAyF;QACzF,yCAAyC;QACzC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,UAAU,EAAE,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACxB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAiB,KAAK;QACzC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QAED,MAAM,CAAC,KAAK,CACV,6BAA6B,KAAK,kBAAkB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAC1E,CAAC;QAEF,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACtD,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YAClB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACjC,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACzD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;YAC9B,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACnC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY;QAC7B,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAa;QAC5B,oEAAoE;IACtE,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,KAAW;QAC3B,+DAA+D;IACjE,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAU;QACxB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAgB;QAC7B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;gBAC1B,kCAAkC;gBAClC,IAAI,CAAC,uBAAuB,EAAE,KAAK,EAAE,CAAC;YACxC,CAAC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,CAAC,KAAK,CACV,4CAA4C,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAClE,CAAC;YACF,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAC5B,kEAAkE;gBAClE,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,IAAI,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjD,MAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;gBAChD,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;gBAChC,MAAM;YACR,CAAC;YACD,2EAA2E;YAC3E,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACvE,CAAC;QACD,MAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;QACtC,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YACjC,yCAAyC;YACzC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;CACF;AAED,MAAM,OAAO,qBAAqB;IAChC,WAAW,GAAuB,EAAE,CAAC;IAErC,KAAK;QACH,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;gBACpB,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC;IAED,iBAAiB,CAAC,SAA2B;QAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAED,aAAa,CAAC,UAA8B;QAC1C,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAC7C,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,SAAS,CAAC,QAAQ,EAAE,CAAC;QACvB,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAY;QAC7B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAY;QAC3B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAU;QAC1B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,IAAU;QACxB,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAgB;QAC7B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACzC,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;CACF;AAED,IAAI,gBAAgB,GAA+B,IAAI,CAAC;AACxD,IAAI,iBAAiB,GAA+B,IAAI,CAAC;AAEzD,MAAM,UAAU,eAAe;IAC7B,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,gBAAgB,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAC/C,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,MAAM,UAAU,gBAAgB;IAC9B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,iBAAiB,GAAG,IAAI,mBAAmB,CAAC,eAAe,EAAE,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,iBAAiB,CAAC;AAC3B,CAAC"}