/**
 * Generate an ISO 8601 timestamp of the current time.
 * @returns An ISO 8601 timestamp.
 */
export declare function timeIso(): string;
/**
 * Generate a trace ID by creating a random UUID v4 and removing the dashes. This is the equivalent
 * of `uuid4().hex` in Python and prefixing it with `trace_`.
 * @returns A trace ID.
 */
export declare function generateTraceId(): string;
/**
 * Generate a span ID by creating a random UUID v4 and removing the dashes. This is the equivalent
 * of `uuid4().hex` in Python and prefixing it with `span_`.
 * @returns A span ID.
 */
export declare function generateSpanId(): string;
/**
 * Generate a group ID by creating a random UUID v4 and removing the dashes. This is the equivalent
 * of `uuid4().hex` in Python and prefixing it with `group_`.
 * @returns A group ID.
 */
export declare function generateGroupId(): string;
/**
 * Remove fields that start with an underscore from an object.
 * @param obj - The object to remove private fields from.
 * @returns A new object with private fields removed.
 */
export declare function removePrivateFields(obj: Record<string, any>): Record<string, any>;
