{"version": 3, "file": "spans.mjs", "sourceRoot": "", "sources": ["../../src/tracing/spans.ts"], "names": [], "mappings": "OAAO,MAAM;OAEN,EAAE,cAAc,EAAE,mBAAmB,EAAE,OAAO,EAAE;AAwHvD,MAAM,OAAO,IAAI;IACR,IAAI,GAAG,YAAqB,CAAC;IAEpC,KAAK,CAAQ;IACb,QAAQ,CAAS;IACjB,OAAO,CAAS;IAChB,SAAS,CAAgB;IACzB,UAAU,CAAmB;IAC7B,UAAU,CAAgB;IAC1B,QAAQ,CAAgB;IACxB,MAAM,CAAmB;IAEzB,aAAa,CAAwB;IAErC,YAAY,OAA2B,EAAE,SAA2B;QAClE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,cAAc,EAAE,CAAC;QAClD,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;QAC1C,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC;QACpC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC;QAC5C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC;IAC1C,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,IAAI,YAAY,CAAC,IAA2B;QAC1C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,GAAG;QACD,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrD,OAAO;QACT,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,QAAQ,CAAC,KAAgB;QACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACtB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,KAAK;QACH,MAAM,IAAI,GAAG,IAAI,IAAI,CACnB;YACE,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,SAAS;YACpC,IAAI,EAAE,IAAI,CAAC,QAAQ;YACnB,SAAS,EAAE,IAAI,CAAC,UAAU,IAAI,SAAS;YACvC,OAAO,EAAE,IAAI,CAAC,QAAQ,IAAI,SAAS;YACnC,KAAK,EAAE,IAAI,CAAC,MAAM,IAAI,SAAS;SAChC,EACD,IAAI,CAAC,UAAU,CAChB,CAAC;QACF,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM;QACJ,OAAO;YACL,MAAM,EAAE,IAAI,CAAC,IAAI;YACjB,EAAE,EAAE,IAAI,CAAC,MAAM;YACf,QAAQ,EAAE,IAAI,CAAC,OAAO;YACtB,SAAS,EAAE,IAAI,CAAC,QAAQ;YACxB,UAAU,EAAE,IAAI,CAAC,SAAS;YAC1B,QAAQ,EAAE,IAAI,CAAC,OAAO;YACtB,SAAS,EAAE,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC7C,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC;IACJ,CAAC;CACF;AAED,MAAM,OAAO,QAAqC,SAAQ,IAAe;IACvE,YAAY,IAAe,EAAE,SAA2B;QACtD,KAAK,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,SAAS,CAAC,CAAC;IAChE,CAAC;IAED,KAAK;QACH,OAAO;IACT,CAAC;IAED,GAAG;QACD,OAAO;IACT,CAAC;IAED,QAAQ;QACN,OAAO;IACT,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC;IACd,CAAC;CACF"}