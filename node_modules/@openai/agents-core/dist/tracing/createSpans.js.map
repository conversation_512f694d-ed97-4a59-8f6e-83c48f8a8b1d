{"version": 3, "file": "createSpans.js", "sourceRoot": "", "sources": ["../../src/tracing/createSpans.ts"], "names": [], "mappings": "OACO,EACL,gBAAgB,EAChB,cAAc,EACd,kBAAkB,GACnB;OACM,EAAqB,sBAAsB,EAAE;AAoBpD,SAAS,gBAAgB,CAGvB,UAA+B;IAC/B,OAAO,KAAK,EACV,EAA2C,EAC3C,GAAG,IAAqC,EACxC,EAAE;QACF,qFAAqF;QACrF,OAAO,kBAAkB,CAAC,KAAK,IAAI,EAAE;YACnC,MAAM,IAAI,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;YACjC,cAAc,CAAC,IAAI,CAAC,CAAC;YACrB,IAAI,CAAC;gBACH,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,QAAQ,CAAC;oBACZ,OAAO,EAAE,KAAK,CAAC,OAAO;oBACtB,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAC;gBACH,MAAM,KAAK,CAAC;YACd,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,GAAG,EAAE,CAAC;gBACX,gBAAgB,EAAE,CAAC;YACrB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,UAAU,kBAAkB,CAChC,OAAsC,EACtC,MAA0B;IAE1B,OAAO,GAAG,EAAE,CAAC;IACb,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,GAAG,OAAO,CAAC,IAAI;SAChB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,gBAAgB,CAG9C,kBAAkB,CAAC,CAAC;AAEtB;;;;;;;;;GASG;AACH,MAAM,UAAU,eAAe,CAC7B,OAAmC,EACnC,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,IAAI,OAAO;YACpC,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,aAAa,GAAG,gBAAgB,CAG3C,eAAe,CAAC,CAAC;AAEnB;;;;;;;;;GASG;AACH,MAAM,UAAU,kBAAkB,CAChC,OAAkE,EAClE,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,UAAU;YAChB,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE;YACjC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE;YACnC,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,gBAAgB,CAG9C,kBAAkB,CAAC,CAAC;AAEtB;;;;;;;;;GASG;AACH,MAAM,UAAU,iBAAiB,CAC/B,OAAqC,EACrC,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE;KAC5C,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,gBAAgB,CAG7C,iBAAiB,CAAC,CAAC;AAErB;;;;;;;GAOG;AACH,MAAM,UAAU,oBAAoB,CAClC,OAAwC,EACxC,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,YAAY;YAClB,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,wEAAwE;AACxE,MAAM,CAAC,MAAM,kBAAkB,GAAG,gBAAgB,CAGhD,oBAAoB,CAAC,CAAC;AAExB;;;GAGG;AACH,MAAM,UAAU,gBAAgB,CAC9B,OAAgE,EAChE,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,EAAE;YACR,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,gBAAgB,CAG5C,gBAAgB,CAAC,CAAC;AAEpB;;;GAGG;AACH,MAAM,UAAU,mBAAmB,CACjC,OAAmE,EACnE,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;YACjB,SAAS,EAAE,KAAK;YAChB,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,iBAAiB,GAAG,gBAAgB,CAG/C,mBAAmB,CAAC,CAAC;AAEvB;;GAEG;AACH,MAAM,UAAU,uBAAuB,CACrC,OAEC,EACD,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,eAAe;YACrB,GAAG,OAAO,CAAC,IAAI;SAChB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,qBAAqB,GAAG,gBAAgB,CAGnD,uBAAuB,CAAC,CAAC;AAE3B;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAC9B,OAEC,EACD,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;YACd,GAAG,OAAO,CAAC,IAAI;SAChB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,cAAc,GAAG,gBAAgB,CAG5C,gBAAgB,CAAC,CAAC;AAEpB;;GAEG;AACH,MAAM,UAAU,qBAAqB,CACnC,OAAyC,EACzC,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,cAAc;YACpB,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,mBAAmB,GAAG,gBAAgB,CAGjD,qBAAqB,CAAC,CAAC;AAEzB;;GAEG;AACH,MAAM,UAAU,sBAAsB,CACpC,OAA0C,EAC1C,MAA0B;IAE1B,OAAO,sBAAsB,EAAE,CAAC,UAAU,CACxC;QACE,GAAG,OAAO;QACV,IAAI,EAAE;YACJ,IAAI,EAAE,WAAW;YACjB,GAAG,OAAO,EAAE,IAAI;SACjB;KACF,EACD,MAAM,CACP,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,MAAM,oBAAoB,GAAG,gBAAgB,CAGlD,sBAAsB,CAAC,CAAC"}