{"version": 3, "file": "provider.mjs", "sourceRoot": "", "sources": ["../../src/tracing/provider.ts"], "names": [], "mappings": "OAAO,EAAE,cAAc,EAAE,eAAe,EAAE;OACnC,EAAE,OAAO,EAAE;OACX,MAAM;OACN,EAAE,qBAAqB,EAAoB;OAC3C,EAAE,QAAQ,EAAE,IAAI,EAAyB;OACzC,EAAE,SAAS,EAAE,KAAK,EAAgB;OAClC,EAAE,eAAe,EAAE;AAO1B,MAAM,OAAO,aAAa;IACxB,eAAe,CAAwB;IACvC,SAAS,CAAU;IAEnB;QACE,IAAI,CAAC,eAAe,GAAG,IAAI,qBAAqB,EAAE,CAAC;QACnD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;QAElC,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,SAA2B;QAC3C,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IACpD,CAAC;IAED;;;;OAIG;IACH,aAAa,CAAC,UAA8B;QAC1C,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACjD,CAAC;IAED;;;;OAIG;IACH,eAAe;QACb,OAAO,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,cAAc;QACZ,OAAO,cAAc,EAAE,CAAC;IAC1B,CAAC;IAED,WAAW,CAAC,QAAiB;QAC3B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAED,eAAe;QACb,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,WAAW,CAAC,YAA0B;QACpC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,CAAC,KAAK,CAAC,4CAA4C,EAAE,YAAY,CAAC,CAAC;YACzE,OAAO,IAAI,SAAS,EAAE,CAAC;QACzB,CAAC;QAED,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,eAAe,EAAE,CAAC;QAC1D,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,IAAI,gBAAgB,CAAC;QAEnD,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;QAE9D,OAAO,IAAI,KAAK,CAAC,EAAE,GAAG,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7E,CAAC;IAED,UAAU,CACR,WAAyC,EACzC,MAA0B;QAE1B,IAAI,IAAI,CAAC,SAAS,IAAI,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC3C,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,WAAW,CAAC,CAAC;YACvE,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,QAAQ,CAAC;QACb,IAAI,OAAO,CAAC;QAEZ,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,cAAc,EAAE,CAAC;YAErC,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClB,MAAM,CAAC,KAAK,CACV,2FAA2F,CAC5F,CAAC;gBACF,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9D,CAAC;YAED,IACE,WAAW,YAAY,QAAQ;gBAC/B,YAAY,YAAY,SAAS,EACjC,CAAC;gBACD,MAAM,CAAC,KAAK,CACV,UAAU,WAAW,OAAO,YAAY,+BAA+B,CACxE,CAAC;gBACF,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;YAC/B,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;gBACzD,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC;YAChC,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,KAAK,CACV,wCAAwC,EACxC,YAAY,CAAC,OAAO,CACrB,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;YACnC,IAAI,MAAM,YAAY,SAAS,EAAE,CAAC;gBAChC,MAAM,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBAC1D,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC3B,CAAC;aAAM,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;YAClC,IAAI,MAAM,YAAY,QAAQ,EAAE,CAAC;gBAC/B,MAAM,CAAC,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBACzD,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9D,CAAC;YAED,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;YACzB,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAC3B,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,CAAC,KAAK,CACV,4FAA4F,CAC7F,CAAC;YACF,OAAO,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,CAAC,KAAK,CACV,iBAAiB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,WAAW,CAAC,MAAM,IAAI,OAAO,EAAE,CAC7F,CAAC;QAEF,OAAO,IAAI,IAAI,CACb;YACE,GAAG,WAAW;YACd,OAAO;YACP,QAAQ;SACT,EACD,IAAI,CAAC,eAAe,CACrB,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAgB;QAC7B,IAAI,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;YAC/C,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,2EAA2E;IAC3E,oBAAoB;QAClB,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,UAAU,EAAE,CAAC;YACvE,uCAAuC;YACvC,MAAM,OAAO,GAAG,KAAK,IAAI,EAAE;gBACzB,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC9B,OAAO,CAAC,IAAI,CAAC,+BAA+B,CAAC,CAAC;oBAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC,EAAE,IAAI,CAAC,CAAC;gBAET,IAAI,CAAC;oBACH,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxB,CAAC;wBAAS,CAAC;oBACT,YAAY,CAAC,OAAO,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC;YAEF,4BAA4B;YAC5B,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;YAElC,yBAAyB;YACzB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;gBAC9B,MAAM,OAAO,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,CAAC,CAAC,CAAC;YAEH,+BAA+B;YAC/B,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;gBAC/B,MAAM,OAAO,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;gBACzD,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;gBACrD,MAAM,OAAO,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC;IAC1C,CAAC;CACF;AAED,IAAI,qBAAqB,GAA8B,SAAS,CAAC;AACjE,MAAM,UAAU,sBAAsB;IACpC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3B,qBAAqB,GAAG,IAAI,aAAa,EAAE,CAAC;IAC9C,CAAC;IACD,OAAO,qBAAqB,CAAC;AAC/B,CAAC"}