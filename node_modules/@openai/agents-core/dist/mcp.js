import { tool } from "./tool.js";
import { UserError } from "./errors.js";
import { MCPServerStdio as UnderlyingMCPServerStdio, MCPServerStreamableHttp as UnderlyingMCPServerStreamableHttp, } from '@openai/agents-core/_shims';
import { getCurrentSpan, withMCPListToolsSpan } from "./tracing/index.js";
import { logger as globalLogger, getLogger } from "./logger.js";
import debug from 'debug';
import { z } from '@openai/zod/v3';
export const DEFAULT_STDIO_MCP_CLIENT_LOGGER_NAME = 'openai-agents:stdio-mcp-client';
export const DEFAULT_STREAMABLE_HTTP_MCP_CLIENT_LOGGER_NAME = 'openai-agents:streamable-http-mcp-client';
export class BaseMCPServerStdio {
    cacheToolsList;
    _cachedTools = undefined;
    logger;
    constructor(options) {
        this.logger =
            options.logger ?? getLogger(DEFAULT_STDIO_MCP_CLIENT_LOGGER_NAME);
        this.cacheToolsList = options.cacheToolsList ?? true;
    }
    /**
     * Logs a debug message when debug logging is enabled.
     * @param buildMessage A function that returns the message to log.
     */
    debugLog(buildMessage) {
        if (debug.enabled(this.logger.namespace)) {
            // only when this is true, the function to build the string is called
            this.logger.debug(buildMessage());
        }
    }
}
export class BaseMCPServerStreamableHttp {
    cacheToolsList;
    _cachedTools = undefined;
    logger;
    constructor(options) {
        this.logger =
            options.logger ??
                getLogger(DEFAULT_STREAMABLE_HTTP_MCP_CLIENT_LOGGER_NAME);
        this.cacheToolsList = options.cacheToolsList ?? true;
    }
    /**
     * Logs a debug message when debug logging is enabled.
     * @param buildMessage A function that returns the message to log.
     */
    debugLog(buildMessage) {
        if (debug.enabled(this.logger.namespace)) {
            // only when this is true, the function to build the string is called
            this.logger.debug(buildMessage());
        }
    }
}
/**
 * Minimum MCP tool data definition.
 * This type definition does not intend to cover all possible properties.
 * It supports the properties that are used in this SDK.
 */
export const MCPTool = z.object({
    name: z.string(),
    description: z.string().optional(),
    inputSchema: z.object({
        type: z.literal('object'),
        properties: z.record(z.string(), z.any()),
        required: z.array(z.string()),
        additionalProperties: z.boolean(),
    }),
});
/**
 * Public interface of an MCP server that provides tools.
 * You can use this class to pass MCP server settings to your agent.
 */
export class MCPServerStdio extends BaseMCPServerStdio {
    underlying;
    constructor(options) {
        super(options);
        this.underlying = new UnderlyingMCPServerStdio(options);
    }
    get name() {
        return this.underlying.name;
    }
    connect() {
        return this.underlying.connect();
    }
    close() {
        return this.underlying.close();
    }
    async listTools() {
        if (this.cacheToolsList && this._cachedTools) {
            return this._cachedTools;
        }
        const tools = await this.underlying.listTools();
        if (this.cacheToolsList) {
            this._cachedTools = tools;
        }
        return tools;
    }
    callTool(toolName, args) {
        return this.underlying.callTool(toolName, args);
    }
}
export class MCPServerStreamableHttp extends BaseMCPServerStreamableHttp {
    underlying;
    constructor(options) {
        super(options);
        this.underlying = new UnderlyingMCPServerStreamableHttp(options);
    }
    get name() {
        return this.underlying.name;
    }
    connect() {
        return this.underlying.connect();
    }
    close() {
        return this.underlying.close();
    }
    async listTools() {
        if (this.cacheToolsList && this._cachedTools) {
            return this._cachedTools;
        }
        const tools = await this.underlying.listTools();
        if (this.cacheToolsList) {
            this._cachedTools = tools;
        }
        return tools;
    }
    callTool(toolName, args) {
        return this.underlying.callTool(toolName, args);
    }
}
/**
 * Fetches and flattens all tools from multiple MCP servers.
 * Logs and skips any servers that fail to respond.
 */
export async function getAllMcpFunctionTools(mcpServers, convertSchemasToStrict = false) {
    const allTools = [];
    const toolNames = new Set();
    for (const server of mcpServers) {
        const serverTools = await getFunctionToolsFromServer(server, convertSchemasToStrict);
        const serverToolNames = new Set(serverTools.map((t) => t.name));
        const intersection = [...serverToolNames].filter((n) => toolNames.has(n));
        if (intersection.length > 0) {
            throw new UserError(`Duplicate tool names found across MCP servers: ${intersection.join(', ')}`);
        }
        for (const t of serverTools) {
            toolNames.add(t.name);
            allTools.push(t);
        }
    }
    return allTools;
}
const _cachedTools = {};
/**
 * Remove cached tools for the given server so the next lookup fetches fresh data.
 *
 * @param serverName - Name of the MCP server whose cache should be cleared.
 */
export function invalidateServerToolsCache(serverName) {
    delete _cachedTools[serverName];
}
/**
 * Fetches all function tools from a single MCP server.
 */
async function getFunctionToolsFromServer(server, convertSchemasToStrict) {
    if (server.cacheToolsList && _cachedTools[server.name]) {
        return _cachedTools[server.name];
    }
    return withMCPListToolsSpan(async (span) => {
        const mcpTools = await server.listTools();
        span.spanData.result = mcpTools.map((t) => t.name);
        const tools = mcpTools.map((t) => mcpToFunctionTool(t, server, convertSchemasToStrict));
        if (server.cacheToolsList) {
            _cachedTools[server.name] = tools;
        }
        return tools;
    }, { data: { server: server.name } });
}
/**
 * Returns all MCP tools from the provided servers, using the function tool conversion.
 */
export async function getAllMcpTools(mcpServers, convertSchemasToStrict = false) {
    return getAllMcpFunctionTools(mcpServers, convertSchemasToStrict);
}
/**
 * Converts an MCP tool definition to a function tool for the Agents SDK.
 */
export function mcpToFunctionTool(mcpTool, server, convertSchemasToStrict) {
    async function invoke(input, _context) {
        let args = {};
        if (typeof input === 'string' && input) {
            args = JSON.parse(input);
        }
        else if (typeof input === 'object' && input != null) {
            args = input;
        }
        const currentSpan = getCurrentSpan();
        if (currentSpan) {
            currentSpan.spanData['mcp_data'] = { server: server.name };
        }
        const content = await server.callTool(mcpTool.name, args);
        return content.length === 1 ? content[0] : content;
    }
    const schema = {
        type: mcpTool.inputSchema?.type ?? 'object',
        properties: mcpTool.inputSchema?.properties ?? {},
        required: mcpTool.inputSchema?.required ?? [],
        additionalProperties: mcpTool.inputSchema?.additionalProperties ?? false,
    };
    if (convertSchemasToStrict || schema.additionalProperties === true) {
        try {
            const strictSchema = ensureStrictJsonSchema(schema);
            return tool({
                name: mcpTool.name,
                description: mcpTool.description || '',
                parameters: strictSchema,
                strict: true,
                execute: invoke,
            });
        }
        catch (e) {
            globalLogger.warn(`Error converting MCP schema to strict mode: ${e}`);
        }
    }
    const nonStrictSchema = {
        ...schema,
        additionalProperties: true,
    };
    return tool({
        name: mcpTool.name,
        description: mcpTool.description || '',
        parameters: nonStrictSchema,
        strict: false,
        execute: invoke,
    });
}
/**
 * Ensures the given JSON schema is strict (no additional properties, required fields set).
 */
function ensureStrictJsonSchema(schema) {
    const out = {
        ...schema,
        additionalProperties: false,
    };
    if (!out.required)
        out.required = [];
    return out;
}
//# sourceMappingURL=mcp.js.map