{"name": "@openai/agents-core", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.0.9", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "./model": {"require": {"types": "./dist/model.d.ts", "default": "./dist/model.js"}, "types": "./dist/model.d.ts", "default": "./dist/model.mjs"}, "./utils": {"require": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.mjs"}, "./extensions": {"require": {"types": "./dist/extensions/index.d.ts", "default": "./dist/extensions/index.js"}, "types": "./dist/extensions/index.d.ts", "default": "./dist/extensions/index.mjs"}, "./types": {"require": {"types": "./dist/types/index.d.ts", "default": "./dist/types/index.js"}, "types": "./dist/types/index.d.ts", "default": "./dist/types/index.mjs"}, "./_shims": {"workerd": {"require": "./dist/shims/shims-workerd.js", "types": "./dist/shims/shims-workerd.d.ts", "default": "./dist/shims/shims-workerd.mjs"}, "browser": {"require": "./dist/shims/shims-browser.js", "types": "./dist/shims/shims-browser.d.ts", "default": "./dist/shims/shims-browser.mjs"}, "node": {"require": "./dist/shims/shims-node.js", "types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.mjs"}, "require": {"types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.js"}, "types": "./dist/shims/shims-node.d.ts", "default": "./dist/shims/shims-node.mjs"}}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "optionalDependencies": {"@modelcontextprotocol/sdk": "^1.12.0"}, "dependencies": {"@openai/zod": "npm:zod@^3.25.40", "debug": "^4.4.0", "openai": "^5.0.1"}, "peerDependencies": {"zod": "^3.25.40"}, "peerDependenciesMeta": {"zod": {"optional": true}}, "typesVersions": {"*": {"model": ["dist/model.d.ts"], "utils": ["dist/utils/index.d.ts"], "extensions": ["dist/extensions/index.d.ts"], "types": ["dist/types/index.d.ts"], "_shims": ["dist/shims/shims-node.d.ts"]}}, "devDependencies": {"@types/debug": "^4.1.12", "zod": "^3.25.40"}, "files": ["dist"], "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}}