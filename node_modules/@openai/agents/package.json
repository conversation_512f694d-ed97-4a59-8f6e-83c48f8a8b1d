{"name": "@openai/agents", "repository": "https://github.com/openai/openai-agents-js", "homepage": "https://openai.github.io/openai-agents-js/", "version": "0.0.9", "description": "The OpenAI Agents SDK is a lightweight yet powerful framework for building multi-agent workflows.", "author": "OpenAI <<EMAIL>>", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "./realtime": {"require": {"types": "./dist/realtime/index.d.ts", "default": "./dist/realtime/index.js"}, "types": "./dist/realtime/index.d.ts", "default": "./dist/realtime/index.mjs"}, "./utils": {"require": {"types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.js"}, "types": "./dist/utils/index.d.ts", "default": "./dist/utils/index.mjs"}}, "dependencies": {"openai": "^5.0.1", "debug": "^4.4.0", "@openai/agents-core": "0.0.9", "@openai/agents-realtime": "0.0.9", "@openai/agents-openai": "0.0.9"}, "keywords": ["openai", "agents", "ai", "agentic"], "license": "MIT", "devDependencies": {"@types/debug": "^4.1.12"}, "files": ["dist"], "typesVersions": {"*": {"realtime": ["dist/realtime/index.d.ts"], "utils": ["dist/utils/index.d.ts"]}}, "scripts": {"prebuild": "tsx ../../scripts/embedMeta.ts", "build": "tsc", "build-check": "tsc --noEmit -p ./tsconfig.test.json"}}