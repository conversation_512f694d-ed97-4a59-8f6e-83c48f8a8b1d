const { Agent, run, setOpenAIAPI } = require("@openai/agents");
const fs = require('fs/promises');
const { searchAndScrape } = require('./scraper');

const WEBSITES_FILE = 'websites.json';

// 设置环境变量 - 这是 OpenAI Agents SDK 推荐的方法
// process.env.OPENAI_API_KEY = 'sk-4btmATzuMv6rUaGsuLLSZW9tFOCBdTp2bTlhyhoD1sItPfqh';
// process.env.OPENAI_BASE_URL = 'https://ed8bb08d60ae2883776c36fed92281df80d5e070-3000.dstack-prod7.phala.network/v1';

// 设置使用 chat_completions API 而不是默认的 responses API
setOpenAIAPI('chat_completions');
process.env.OPENAI_BASE_URL = 'https://llm.chutes.ai/v1';
process.env.OPENAI_API_KEY = 'cpk_40c43ac21a834e56805800c40b884cb3.9c882f9cfc4e5bc88a66dca3af05cfcb.YJpwrYKDxIsWGAWO5o6296ACSmuLMhTd';

const agent = new Agent({
    name: "AI Bounds Agent",
    model: "deepseek-ai/DeepSeek-R1-0528",
    system: `You are an expert AI assistant. Your task is to analyze the provided text content from a website and determine if it is primarily about Artificial Intelligence. If it is, extract the official name of the AI tool, platform, or company. The name should be the proper noun, not a slogan.
Please respond in JSON format with two keys: "is_ai_related" (boolean) and "name" (string, the official name, or null if not AI-related).`,
    response_format: { type: "json_output" },
});

async function analyzeContentWithAI(content, url) {
    try {
        const userMessage = `Here is the text from ${url}:\n\n"${content.substring(0, 4000)}"\n\nIs this website about AI? If yes, what is its official name?`;
        
        const result = await run(agent, userMessage);

        const parsedResult = JSON.parse(result.finalOutput);
        return parsedResult;
    } catch (error) {
        console.error(`Error analyzing content for ${url}:`, error);
        return { is_ai_related: false, name: null };
    }
}

async function scrapeAndProcess(query) {
    console.log(`Starting scraping process for query: "${query}"`);
    const scrapedData = await searchAndScrape(query);
    const processedWebsites = [];

    let existingData = [];
    try {
        const fileContent = await fs.readFile(WEBSITES_FILE, 'utf-8');
        existingData = JSON.parse(fileContent);
    } catch (error) {
        if (error.code !== 'ENOENT') {
            console.error('Error reading existing websites file:', error);
        }
        // If file doesn't exist, it's fine, we'll create it.
    }

    const existingUrls = new Set(existingData.map(site => site.url));

    for (const item of scrapedData) {
        if (existingUrls.has(item.url)) {
            console.log(`Skipping already processed URL: ${item.url}`);
            continue;
        }

        console.log(`Analyzing content from: ${item.url}`);
        const analysis = await analyzeContentWithAI(item.content, item.url);

        if (analysis.is_ai_related && analysis.name) {
            const websiteData = {
                name: analysis.name,
                url: item.url,
                collected_at: new Date().toISOString()
            };
            processedWebsites.push(websiteData);
            existingUrls.add(item.url); // Add to set to prevent duplicates in the same run
            console.log(`[AI Found] Name: ${analysis.name}, URL: ${item.url}`);
        } else {
            console.log(`[Not AI] Skipping: ${item.url}`);
        }
    }

    if (processedWebsites.length > 0) {
        const updatedData = [...existingData, ...processedWebsites];
        await fs.writeFile(WEBSITES_FILE, JSON.stringify(updatedData, null, 2));
        console.log(`${processedWebsites.length} new AI websites have been saved to ${WEBSITES_FILE}.`);
    } else {
        console.log("No new AI websites were found in this run.");
    }
}

module.exports = { scrapeAndProcess }; 