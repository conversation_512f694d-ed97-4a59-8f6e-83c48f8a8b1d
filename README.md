# AI Bounds Agent

一个智能的AI网站发现和分析工具，使用OpenAI Agents自动搜索、抓取和识别AI相关的网站。

## 🚀 功能特性

- **智能搜索**: 使用DuckDuckGo搜索引擎自动查找相关网站
- **内容抓取**: 自动抓取网站内容并清理无关信息
- **AI分析**: 使用OpenAI Agents智能分析网站是否与AI相关
- **数据存储**: 自动保存发现的AI网站信息到JSON文件
- **RESTful API**: 提供简单的HTTP接口进行操作
- **实时处理**: 支持实时搜索和分析

## 📋 系统要求

- Node.js 16.0 或更高版本
- npm 或 yarn 包管理器
- 有效的OpenAI API密钥或兼容的API服务

## 🛠️ 安装

1. 克隆项目到本地：
```bash
git clone <repository-url>
cd ai-bounds-agent
```

2. 安装依赖：
```bash
npm install
```

3. 配置API密钥：
   - 打开 `agent.js` 文件
   - 修改以下配置：
   ```javascript
   process.env.OPENAI_API_KEY = 'your-api-key-here';
   process.env.OPENAI_BASE_URL = 'your-api-base-url'; // 可选，默认使用OpenAI官方API
   ```

## 🚀 快速开始

1. 启动服务器：
```bash
npm start
```

2. 服务器将在 `http://localhost:3000` 启动

3. 发送搜索请求：
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"query": "AI Agent Frameworks"}' \
  http://localhost:3000/scrape
```

4. 查看发现的AI网站：
```bash
curl http://localhost:3000/websites
```

## 📚 API 文档

### POST /scrape

启动搜索和分析过程。

**请求体：**
```json
{
  "query": "搜索关键词"
}
```

**响应：**
```json
{
  "message": "Scraping and processing initiated."
}
```

**示例：**
```bash
curl -X POST -H "Content-Type: application/json" \
  -d '{"query": "machine learning platforms"}' \
  http://localhost:3000/scrape
```

### GET /websites

获取已发现的AI网站列表。

**响应：**
```json
[
  {
    "name": "OpenAI",
    "url": "https://openai.com",
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
]
```

## 🔧 配置选项

### AI模型配置

在 `agent.js` 中可以配置使用的AI模型：

```javascript
const agent = new Agent({
    name: "AI Bounds Agent",
    model: "deepseek-ai/DeepSeek-R1-0528", // 可以更换为其他模型
    instructions: "...", // AI分析指令
    outputType: { ... } // 输出格式定义
});
```

### 搜索配置

在 `scraper.js` 中可以调整搜索参数：

- 搜索结果数量：修改 `links.slice(0, 5)` 中的数字
- 请求超时时间：修改 `timeout: 10000`
- User-Agent：修改请求头中的User-Agent

## 📁 项目结构

```
ai-bounds-agent/
├── README.md           # 项目文档
├── package.json        # 项目配置和依赖
├── index.js           # Express服务器主文件
├── agent.js           # OpenAI Agent配置和AI分析逻辑
├── scraper.js         # 网站搜索和内容抓取
└── websites.json      # 存储发现的AI网站（自动生成）
```

## 🔍 工作流程

1. **接收搜索请求**: 通过POST /scrape接口接收搜索关键词
2. **搜索网站**: 使用DuckDuckGo搜索相关网站
3. **内容抓取**: 抓取搜索结果中的网站内容
4. **AI分析**: 使用OpenAI Agent分析内容是否与AI相关
5. **提取信息**: 如果是AI相关，提取官方名称
6. **数据存储**: 将结果保存到websites.json文件

## 🛡️ 错误处理

- **网络错误**: 自动跳过无法访问的网站
- **API错误**: 记录错误日志但继续处理其他网站
- **解析错误**: 对于无法解析的内容返回默认值
- **超时处理**: 设置合理的超时时间避免长时间等待

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   - 检查 `agent.js` 中的API密钥是否正确
   - 确认API服务是否可用

2. **搜索结果为空**
   - 尝试更换搜索关键词
   - 检查网络连接

3. **服务器启动失败**
   - 确认端口3000未被占用
   - 检查Node.js版本是否符合要求

### 调试模式

启用详细日志输出：
```bash
DEBUG=* npm start
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

ISC License

## 📞 支持

如果遇到问题，请：
1. 查看故障排除部分
2. 检查项目Issues
3. 创建新的Issue描述问题
